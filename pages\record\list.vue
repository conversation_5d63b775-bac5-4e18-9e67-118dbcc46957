<template>
	<view class="record-list-page">
		<!-- 现代化日期显示区域 -->
		<view class="date-display-container">
			<view class="date-display-card">
				<view class="date-content">
					<view class="date-info-section">
						<text class="date-label">当前查看</text>
						<text class="date-value">{{ currentDateDisplay }}</text>
					</view>
					<view class="date-actions-section" v-if="!isShowingToday">
						<button class="back-to-today-btn" @click="backToToday">
							<view class="btn-content">
								<text class="btn-text">回到今天</text>
							</view>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 日统计卡片 -->
		<DailyStatsCard :stats="dailyStats" :dateRange="filters.dateRange" :isToday="isShowingToday"
			@stat-click="onDailyStatClick" v-if="shouldShowDailyStats" />

		<!-- 操作按钮 - 统一样式 -->
		<view class="enhanced-action-section" v-if="isAdmin && records.length > 0">
			<view class="action-buttons-card">
				<view class="action-buttons-grid">
					<button class="enhanced-action-btn export-btn" :class="{ 'loading': exporting }"
						:disabled="exporting" @click="exportRecordList">
						<view class="btn-content">
							<text class="btn-icon" v-if="!exporting">📤</text>
							<text class="btn-icon" v-else>⏳</text>
							<text class="btn-text">{{ exporting ? '导出中...' : '导出数据' }}</text>
						</view>
					</button>
					<button class="enhanced-action-btn print-btn" @click="printRecordList">
						<view class="btn-content">
							<text class="btn-icon">🖨️</text>
							<text class="btn-text">打印报告</text>
						</view>
					</button>
				</view>
			</view>
		</view>

		<!-- 现代化搜索和筛选区域 -->
		<view class="search-filter-container">
			<view class="search-filter-card">
				<!-- 搜索栏 -->
				<view class="search-bar">
					<view class="search-input-wrapper">
						<text class="search-icon">🔍</text>
						<input class="search-input" type="text" placeholder="搜索工人姓名..." v-model="searchKeyword"
							@input="onSearchInput" @focus="onSearchFocus" @blur="onSearchBlur" />
						<text class="clear-search-btn" v-if="searchKeyword" @click="clearSearch">✕</text>
					</view>
					<view class="filter-btn" @click="showFilterModal = true">
						<text class="filter-icon">⚙️</text>
						<text class="filter-text">筛选</text>
						<view class="filter-badge" v-if="hasActiveFilters">{{ activeFilterCount }}</view>
					</view>
				</view>

				<!-- 快速筛选标签 -->
				<view class="quick-filters">
					<view class="filter-tabs">
						<view class="filter-tab" :class="{ active: filters.workMode === 'all' }"
							@click="setWorkModeFilter('all')">
							<text class="tab-text">全部</text>
						</view>
						<view class="filter-tab" :class="{ active: filters.workMode === 'tea_picking' }"
							@click="setWorkModeFilter('tea_picking')">
							<text class="tab-icon">🍃</text>
							<text class="tab-text">采茶</text>
						</view>
						<view class="filter-tab" :class="{ active: filters.workMode === 'hourly' }"
							@click="setWorkModeFilter('hourly')">
							<text class="tab-icon">⏰</text>
							<text class="tab-text">时工</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 记录列表 -->
		<view class="record-list">
			<view class="record-item" v-for="record in records" :key="record.id" @click="viewRecord(record)">
				<view class="record-header">
					<view class="worker-info">
						<text class="worker-name">{{ record.worker_name }}</text>
						<view class="work-mode-badge" :class="record.work_mode">
							<text>{{ getModeText(record.work_mode) }}</text>
						</view>
					</view>
					<view class="record-actions" v-if="isAdmin" @click.stop>
						<!-- 🔧 调试：添加调试信息显示 -->
						<text class="debug-info" style="font-size: 20rpx; color: #999; margin-right: 10rpx;">
							Admin: {{ isAdmin ? '✓' : '✗' }}
						</text>
						<text class="action-btn delete" @click="deleteRecord(record)"
							style="padding: 10rpx; background: #ff4444; color: white; border-radius: 8rpx;">🗑️</text>
						<!-- 🔧 调试：添加测试按钮 -->
						<text class="action-btn test" @click="testDeleteFunction(record)"
							style="padding: 10rpx; background: #4CAF50; color: white; border-radius: 8rpx; margin-left: 10rpx;">测试</text>
					</view>
				</view>

				<view class="record-content">
					<view class="record-date">
						<text class="date-text">📅 {{ $formatDate(record.date) }}</text>
					</view>

					<!-- 采茶模式详情 -->
					<view class="record-details" v-if="record.work_mode === 'tea_picking'">
						<view class="detail-row">
							<view class="detail-item">
								<text class="detail-label">🍃 类型：</text>
								<text class="detail-value">{{ getTeaProjectSummary(record) }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">📦 原斤数：</text>
								<text class="detail-value">{{ getTeaOriginalWeight(record) }}斤</text>
							</view>
						</view>
						<view class="detail-row">
							<view class="detail-item">
								<text class="detail-label">⚖️ 实际斤数：</text>
								<text class="detail-value">{{ getTeaActualWeight(record) }}斤</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">💰 均单价：</text>
								<text class="detail-value">¥{{ getTeaAveragePrice(record) }}/斤</text>
							</view>
						</view>
					</view>

					<!-- 时工模式详情 -->
					<view class="record-details" v-if="record.work_mode === 'hourly'">
						<view class="detail-row">
							<view class="detail-item">
								<text class="detail-label">⏱️ 工作时长：</text>
								<text class="detail-value">{{ getHourlyTotalHours(record) }}小时</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">💰 平均时薪：</text>
								<text class="detail-value">¥{{ getHourlyAverageRate(record) }}/时</text>
							</view>
						</view>
						<view class="detail-row" v-if="getHourlyWorkMode(record)">
						</view>
					</view>
				</view>

				<view class="record-footer">
					<text class="earnings-label">工钱：</text>
					<text class="earnings-value">¥{{ $formatCurrency(record.total_earnings) }}</text>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-if="records.length === 0 && !loading">
				<text class="empty-icon">📋</text>
				<text class="empty-text">暂无记录</text>
				<text class="empty-hint" v-if="isAdmin">点击右上角"+"添加记录</text>
			</view>
		</view>

		<!-- 分页 -->
		<view class="pagination" v-if="pagination.total > pagination.limit">
			<button class="page-btn" :disabled="pagination.page <= 1" @click="loadPage(pagination.page - 1)">
				上一页
			</button>
			<text class="page-info">{{ pagination.page }} / {{ totalPages }}</text>
			<button class="page-btn" :disabled="pagination.page >= totalPages" @click="loadPage(pagination.page + 1)">
				下一页
			</button>
		</view>

		<!-- 添加按钮 -->
		<view class="fab" v-if="isAdmin" @click="goToAddRecord">
			<text class="fab-icon">+</text>
		</view>

		<!-- 筛选弹窗 -->
		<view class="filter-modal" v-if="showFilterModal" @click="showFilterModal = false">
			<view class="filter-content" @click.stop>
				<view class="filter-header">
					<text class="filter-title">高级筛选</text>
					<text class="filter-close" @click="showFilterModal = false">✕</text>
				</view>

				<view class="filter-body">
					<view class="filter-group">
						<text class="filter-group-title">日期范围</text>
						<view class="date-range">
							<picker mode="date" :value="tempFilters.startDate" @change="onStartDateChange">
								<view class="date-picker">
									<text>{{ tempFilters.startDate || '开始日期' }}</text>
								</view>
							</picker>
							<text class="date-separator">至</text>
							<picker mode="date" :value="tempFilters.endDate" @change="onEndDateChange">
								<view class="date-picker">
									<text>{{ tempFilters.endDate || '结束日期' }}</text>
								</view>
							</picker>
						</view>
					</view>

					<view class="filter-group" v-if="filters.workMode === 'tea_picking'">
						<text class="filter-group-title">采茶筛选</text>

						<!-- 时间段多选筛选 -->
						<view class="filter-section">
							<view class="filter-section-header">
								<text class="filter-section-title">时间段</text>
								<view class="filter-section-actions">
									<text class="filter-action-btn" @click="selectAllTimePeriods">全选</text>
									<text class="filter-action-btn" @click="clearTimePeriods">清空</text>
								</view>
							</view>
							<view class="filter-button-group">
								<view class="filter-multi-btn"
									:class="{ active: tempFilters.selectedTimePeriods.includes('morning') }"
									@click="toggleTimePeriod('morning')">
									<text class="btn-icon">🌅</text>
									<text class="btn-text">上午</text>
								</view>

								<view class="filter-multi-btn"
									:class="{ active: tempFilters.selectedTimePeriods.includes('afternoon') }"
									@click="toggleTimePeriod('afternoon')">
									<text class="btn-icon">🌇</text>
									<text class="btn-text">下午</text>
								</view>
							</view>
						</view>

						<!-- 项目类型多选筛选 -->
						<view class="filter-section">
							<view class="filter-section-header">
								<text class="filter-section-title">项目类型</text>
								<view class="filter-section-actions">
									<text class="filter-action-btn" @click="selectAllProjects">全选</text>
									<text class="filter-action-btn" @click="clearProjects">清空</text>
								</view>
							</view>
							<view class="filter-button-group">
								<view class="filter-multi-btn"
									:class="{ active: tempFilters.selectedProjects.includes('one') }"
									@click="toggleProject('one')">
									<text class="btn-icon">🍃</text>
									<text class="btn-text">一叶</text>
								</view>
								<view class="filter-multi-btn"
									:class="{ active: tempFilters.selectedProjects.includes('two') }"
									@click="toggleProject('two')">
									<text class="btn-icon">🌿</text>
									<text class="btn-text">二叶</text>
								</view>
								<view class="filter-multi-btn"
									:class="{ active: tempFilters.selectedProjects.includes('three') }"
									@click="toggleProject('three')">
									<text class="btn-icon">🍀</text>
									<text class="btn-text">三叶</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="filter-footer">
					<button class="filter-btn-secondary" @click="resetFilters">重置</button>
					<button class="filter-btn-primary" @click="applyFilters">应用</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import printManager from '@/utils/print.js'
import recordExporter from '@/utils/export.js'
import DailyStatsCard from '@/components/DailyStatsCard.vue'
import SalesManager from '@/utils/salesManager.js'
import debugHelper from '@/utils/debugHelper.js' // 🔧 调试：引入调试助手

export default {
	name: 'RecordListPage',
	components: {
		DailyStatsCard
	},
	data() {
		return {
			searchKeyword: '',
			searchFocused: false, // 搜索框聚焦状态
			showFilterModal: false,
			loading: false,
			exporting: false, // 导出状态
			$salesManager: null, // 🔧 修复：延迟初始化销售记录管理器实例
			$debugHelper: debugHelper, // 🔧 调试：调试助手实例
			tempFilters: {
				startDate: '',
				endDate: '',
				timePeriodIndex: 0,
				projectIndex: 0,
				// 新增多选筛选状态
				selectedTimePeriods: [], // 选中的时间段数组
				selectedProjects: [] // 选中的项目类型数组
			},
			timePeriodOptions: ['全部', '上午', '下午'],
			projectOptions: ['全部', '一叶', '二叶', '三叶'],
			dailyStats: {
				totalRecords: 0,
				totalEarnings: 0,
				teaRecords: 0,
				teaEarnings: 0,
				totalWeight: 0,
				hourlyRecords: 0,
				hourlyEarnings: 0,
				totalHours: 0
			}
		}
	},
	computed: {
		...mapGetters('user', ['isAdmin']),
		...mapGetters('record', ['records', 'filters', 'pagination']),

		totalPages() {
			return Math.ceil(this.pagination.total / this.pagination.limit)
		},

		// 当前日期显示
		currentDateDisplay() {
			if (this.isShowingToday) {
				const today = new Date()
				return this.formatDateToYYYYMMDD(today)
			} else if (this.filters.dateRange && this.filters.dateRange.length === 2) {
				const start = this.filters.dateRange[0]
				const end = this.filters.dateRange[1]
				if (start === end) {
					// 格式化单个日期，显示完整的年-月-日格式
					const date = new Date(start)
					return this.formatDateToYYYYMMDD(date)
				} else {
					// 格式化日期范围，显示完整的年-月-日格式
					const startDate = new Date(start)
					const endDate = new Date(end)
					const startStr = this.formatDateToYYYYMMDD(startDate)
					const endStr = this.formatDateToYYYYMMDD(endDate)
					return `${startStr} 至 ${endStr}`
				}
			} else {
				return '全部日期'
			}
		},

		// 是否正在显示今天的数据
		isShowingToday() {
			if (!this.filters.dateRange || this.filters.dateRange.length !== 2) {
				return false
			}
			const today = this.getTodayDateString()
			return this.filters.dateRange[0] === today && this.filters.dateRange[1] === today
		},

		hasActiveFilters() {
			return this.filters.name ||
				this.filters.workMode !== 'all' ||
				(this.filters.dateRange && this.filters.dateRange.length > 0)
		},

		activeFilterCount() {
			let count = 0
			if (this.filters.name) count++
			if (this.filters.workMode !== 'all') count++
			if (this.filters.dateRange && this.filters.dateRange.length > 0) count++
			// 添加采茶模式多选筛选计数
			if (this.filters.selectedTimePeriods && this.filters.selectedTimePeriods.length > 0) count++
			if (this.filters.selectedProjects && this.filters.selectedProjects.length > 0) count++
			return count
		},

		// 是否应该显示日统计卡片
		shouldShowDailyStats() {
			// 当有日期筛选时显示统计卡片
			return this.filters.dateRange && this.filters.dateRange.length > 0
		},

		// 是否正在显示今日数据
		isShowingToday() {
			if (!this.filters.dateRange || this.filters.dateRange.length === 0) {
				return false
			}

			const today = this.getTodayDateString()
			return this.filters.dateRange.length === 2 &&
				this.filters.dateRange[0] === today &&
				this.filters.dateRange[1] === today
		}
	},
	onLoad() {
		this.initSalesManager() // 🔧 修复：初始化销售管理器
		this.initFilters()
		this.setDefaultToToday()
		this.fixExistingData() // 修复现有数据
		this.checkLocalStorageData() // 添加数据检查
		this.checkUserStatus() // 🔧 调试：检查用户状态
		this.loadRecords()
	},
	onShow() {
		this.refreshRecords()
		this.fetchDailyStats()
		// 添加延迟测试，确保数据加载完成后再测试
		setTimeout(() => {
			this.testDataProcessing()
		}, 2000)
	},
	onPullDownRefresh() {
		this.refreshRecords().finally(() => {
			uni.stopPullDownRefresh()
		})
	},
	onReachBottom() {
		this.loadMoreRecords()
	},
	methods: {
		...mapActions('record', ['fetchRecords', 'setFilters']),

		// 🔧 修复：初始化销售管理器
		initSalesManager() {
			console.log('🔧 [初始化] 开始初始化SalesManager')
			console.log('🔧 [初始化] SalesManager类可用性:', typeof SalesManager)
			console.log('🔧 [初始化] SalesManager类:', SalesManager)

			try {
				// 检查SalesManager类是否可用
				if (typeof SalesManager === 'undefined') {
					console.error('❌ [初始化] SalesManager类未定义，尝试动态导入')
					// 延迟初始化，在使用时再创建
					this.$salesManager = null
					return
				}

				this.$salesManager = new SalesManager()
				console.log('✅ [初始化] SalesManager初始化成功:', this.$salesManager)
				console.log('✅ [初始化] SalesManager.salesDB:', this.$salesManager.salesDB)

				// 验证关键方法是否存在
				if (this.$salesManager.salesDB && typeof this.$salesManager.salesDB.getSalesRecordsByDate === 'function') {
					console.log('✅ [初始化] 关键方法验证成功')
				} else {
					console.error('❌ [初始化] 关键方法缺失')
					this.$salesManager = null
				}

			} catch (error) {
				console.error('❌ [初始化] SalesManager初始化失败:', error)
				console.error('❌ [初始化] 错误详情:', error.message)
				console.error('❌ [初始化] 错误堆栈:', error.stack)
				// 设置为null，在使用时会重新尝试创建
				this.$salesManager = null
			}
		},

		// 🔧 新增：获取或创建SalesManager实例
		getSalesManager() {
			console.log('🔧 [获取SalesManager] 开始获取SalesManager实例')

			// 如果已经有实例，直接返回
			if (this.$salesManager && this.$salesManager.salesDB) {
				console.log('✅ [获取SalesManager] 使用现有实例')
				return this.$salesManager
			}

			// 尝试创建新实例
			try {
				console.log('🔧 [获取SalesManager] 尝试创建新实例')
				console.log('🔧 [获取SalesManager] SalesManager类:', typeof SalesManager, SalesManager)

				if (typeof SalesManager === 'undefined') {
					console.error('❌ [获取SalesManager] SalesManager类未定义')
					return null
				}

				const newInstance = new SalesManager()
				console.log('✅ [获取SalesManager] 新实例创建成功:', newInstance)

				// 验证实例
				if (newInstance && newInstance.salesDB && typeof newInstance.salesDB.getSalesRecordsByDate === 'function') {
					this.$salesManager = newInstance
					console.log('✅ [获取SalesManager] 实例验证成功并已保存')
					return newInstance
				} else {
					console.error('❌ [获取SalesManager] 实例验证失败')
					return null
				}

			} catch (error) {
				console.error('❌ [获取SalesManager] 创建实例失败:', error)
				return null
			}
		},

		// 修复现有数据格式
		fixExistingData() {
			console.log('=== 开始修复现有数据格式 ===')
			try {
				const workRecords = uni.getStorageSync('workRecords') || []
				let hasChanges = false

				const fixedRecords = workRecords.map(record => {
					if (record.work_mode === 'tea_picking' && record.tea_picking_details) {
						// 检查是否是错误的类数组对象格式
						if (!Array.isArray(record.tea_picking_details) && typeof record.tea_picking_details === 'object') {
							const keys = Object.keys(record.tea_picking_details)
							const numericKeys = keys.filter(key => /^\d+$/.test(key)).sort((a, b) => parseInt(a) - parseInt(b))

							if (numericKeys.length > 0) {
								console.log(`修复记录 ${record.id} 的数据格式`)
								// 将类数组对象转换为真正的数组
								const arrayData = numericKeys.map(key => record.tea_picking_details[key])
								record.tea_picking_details = arrayData
								hasChanges = true
							}
						}
					}
					return record
				})

				if (hasChanges) {
					uni.setStorageSync('workRecords', fixedRecords)
					console.log('数据格式修复完成，已保存到本地存储')
				} else {
					console.log('没有需要修复的数据')
				}
			} catch (error) {
				console.error('修复数据格式失败:', error)
			}
			console.log('=== 数据格式修复完成 ===')
		},

		// 检查本地存储数据
		checkLocalStorageData() {
			console.log('=== 检查本地存储数据 ===')
			try {
				const workRecords = uni.getStorageSync('workRecords') || []
				console.log('本地存储记录总数:', workRecords.length)

				const teaRecords = workRecords.filter(r => r.work_mode === 'tea_picking')
				console.log('本地存储采茶记录数:', teaRecords.length)

				if (teaRecords.length === 0) {
					console.log('没有采茶记录，创建测试数据')
					this.createTestTeaRecord()
				} else {
					teaRecords.forEach((record, index) => {
						console.log(`本地采茶记录 ${index + 1}:`, {
							id: record.id,
							worker_name: record.worker_name,
							date: record.date,
							total_earnings: record.total_earnings,
							tea_picking_details: record.tea_picking_details,
							tea_picking_details_type: typeof record.tea_picking_details,
							tea_picking_details_isArray: Array.isArray(record.tea_picking_details),
							tea_picking_details_keys: record.tea_picking_details ? Object.keys(record.tea_picking_details) : null
						})
					})
				}
			} catch (error) {
				console.error('检查本地存储数据失败:', error)
			}
			console.log('=== 本地存储数据检查完成 ===')
		},

		// 创建测试采茶记录
		createTestTeaRecord() {
			console.log('=== 创建测试采茶记录 ===')
			try {
				const testRecord = {
					id: Date.now(),
					admin_user_id: 1,
					date: '2025-01-23',
					worker_name: '测试工人',
					work_mode: 'tea_picking',
					total_earnings: 85.40,
					tea_picking_details: [
						{
							id: Date.now() + 1,
							work_record_id: Date.now(),
							time_period: 'morning',
							original_weight: 10.5,
							project: 'two',
							price: 4.2,
							moisture_deduction: 0.5,
							actual_weight: 10.0,
							earnings: 42.0
						},
						{
							id: Date.now() + 2,
							work_record_id: Date.now(),
							time_period: 'afternoon',
							original_weight: 12.0,
							project: 'one',
							price: 3.8,
							moisture_deduction: 0.6,
							actual_weight: 11.4,
							earnings: 43.32
						}
					],
					created_at: new Date().toISOString(),
					updated_at: new Date().toISOString()
				}

				const existingRecords = uni.getStorageSync('workRecords') || []
				existingRecords.unshift(testRecord)
				uni.setStorageSync('workRecords', existingRecords)

				console.log('测试记录已创建:', testRecord)
			} catch (error) {
				console.error('创建测试记录失败:', error)
			}
		},

		// 测试数据处理
		testDataProcessing() {
			console.log('=== 开始测试数据处理 ===')

			// 测试当前记录
			const teaRecords = this.records.filter(r => r.work_mode === 'tea_picking')
			console.log('当前采茶记录数量:', teaRecords.length)

			if (teaRecords.length > 0) {
				const testRecord = teaRecords[0]
				console.log('测试记录:', testRecord)

				// 测试各个方法
				console.log('项目摘要:', this.getTeaProjectSummary(testRecord))
				console.log('原斤数:', this.getTeaOriginalWeight(testRecord))
				console.log('实际斤数:', this.getTeaActualWeight(testRecord))
				console.log('平均单价:', this.getTeaAveragePrice(testRecord))
			} else {
				console.log('没有采茶记录可供测试')
			}

			console.log('=== 数据处理测试完成 ===')
		},

		// 初始化筛选器状态
		initFilters() {
			// 同步store中的筛选器状态到临时筛选器
			if (this.filters.dateRange && this.filters.dateRange.length === 2) {
				this.tempFilters.startDate = this.filters.dateRange[0]
				this.tempFilters.endDate = this.filters.dateRange[1]
			}

			// 初始化多选筛选状态
			if (this.filters.selectedTimePeriods) {
				this.tempFilters.selectedTimePeriods = [...this.filters.selectedTimePeriods]
			}
			if (this.filters.selectedProjects) {
				this.tempFilters.selectedProjects = [...this.filters.selectedProjects]
			}
		},

		// 获取今天的日期字符串
		getTodayDateString() {
			const today = new Date()
			const year = today.getFullYear()
			const month = String(today.getMonth() + 1).padStart(2, '0')
			const day = String(today.getDate()).padStart(2, '0')
			return `${year}-${month}-${day}`
		},

		// 格式化日期为 YYYY-MM-DD 格式
		formatDateToYYYYMMDD(date) {
			if (!date) return ''
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			return `${year}-${month}-${day}`
		},

		// 设置默认显示今天
		setDefaultToToday() {
			const today = this.getTodayDateString()
			const newFilters = {
				...this.filters,
				dateRange: [today, today],
				page: 1
			}
			this.setFilters(newFilters)
			this.tempFilters.startDate = today
			this.tempFilters.endDate = today
		},

		// 回到今天（快速操作按钮）
		backToToday() {
			const today = this.getTodayDateString()
			const newFilters = {
				...this.filters,
				dateRange: [today, today],
				name: '', // 清除姓名筛选
				workMode: 'all', // 重置工作模式筛选
				selectedTimePeriods: [], // 清除时间段筛选
				selectedProjects: [], // 清除项目类型筛选
				page: 1
			}
			this.setFilters(newFilters)
			this.tempFilters.startDate = today
			this.tempFilters.endDate = today
			this.tempFilters.selectedTimePeriods = []
			this.tempFilters.selectedProjects = []
			this.searchKeyword = '' // 清除搜索关键字
			this.loadRecords()
			this.fetchDailyStats()
			this.$showToast('已回到今天', 'success')
		},

		// 显示今天的数据
		showToday() {
			const today = this.getTodayDateString()
			const newFilters = {
				...this.filters,
				dateRange: [today, today],
				page: 1
			}
			this.setFilters(newFilters)
			this.tempFilters.startDate = today
			this.tempFilters.endDate = today
			this.loadRecords()
			this.fetchDailyStats()
		},

		// 显示全部日期的数据
		showAllDates() {
			const newFilters = {
				...this.filters,
				dateRange: [],
				page: 1
			}
			this.setFilters(newFilters)
			this.tempFilters.startDate = ''
			this.tempFilters.endDate = ''
			this.loadRecords()
			this.fetchDailyStats()
		},

		async fetchDailyStats() {
			try {
				// 获取所有记录
				const allRecords = uni.getStorageSync('workRecords') || []

				// 根据当前筛选条件筛选记录
				let filteredRecords = allRecords

				// 应用日期范围筛选
				if (this.filters.dateRange && this.filters.dateRange.length > 0) {
					const startDate = this.filters.dateRange[0]
					const endDate = this.filters.dateRange.length > 1 ? this.filters.dateRange[1] : startDate

					filteredRecords = filteredRecords.filter(record => {
						const recordDate = new Date(record.date).toISOString().split('T')[0]
						return recordDate >= startDate && recordDate <= endDate
					})
				}

				// 应用其他筛选条件
				if (this.filters.name) {
					filteredRecords = filteredRecords.filter(record =>
						record.worker_name.includes(this.filters.name)
					)
				}

				if (this.filters.workMode !== 'all') {
					filteredRecords = filteredRecords.filter(record =>
						record.work_mode === this.filters.workMode
					)
				}

				// 计算统计数据 - 支持细粒度筛选
				let totalEarnings = 0
				let teaRecords = 0
				let teaEarnings = 0
				let totalWeight = 0
				let hourlyRecords = 0
				let hourlyEarnings = 0
				let totalHours = 0

				filteredRecords.forEach(record => {
					if (record.work_mode === 'tea_picking') {
						// 采茶记录的细粒度计算
						const { earnings, weight, hasMatchingDetails } = this.calculateTeaStatsForRecord(record)

						if (hasMatchingDetails) {
							teaRecords++
							const safeEarnings = this.safeParseFloat(earnings)
							const safeWeight = this.safeParseFloat(weight)

							teaEarnings += safeEarnings
							totalWeight += safeWeight
							totalEarnings += safeEarnings
						}
					} else if (record.work_mode === 'hourly') {
						// 时工记录直接计算（暂时不需要细粒度筛选）
						hourlyRecords++
						const recordEarnings = this.safeParseFloat(record.total_earnings)
						const recordHours = this.safeParseFloat(record.hourly_work_details?.total_hours)

						hourlyEarnings += recordEarnings
						totalEarnings += recordEarnings
						totalHours += recordHours
					}
				})

				// 更新日统计数据 - 确保所有数值都是安全的
				this.dailyStats = {
					totalRecords: teaRecords + hourlyRecords,
					totalEarnings: this.safeParseFloat(totalEarnings),
					teaRecords: teaRecords,
					teaEarnings: this.safeParseFloat(teaEarnings),
					totalWeight: this.safeParseFloat(totalWeight),
					hourlyRecords: hourlyRecords,
					hourlyEarnings: this.safeParseFloat(hourlyEarnings),
					totalHours: this.safeParseFloat(totalHours)
				}

				// 调试日志
				console.log('统计数据计算完成:', {
					filteredRecordsCount: filteredRecords.length,
					teaRecords,
					teaEarnings,
					hourlyRecords,
					hourlyEarnings,
					totalEarnings,
					dailyStats: this.dailyStats
				})

			} catch (error) {
				console.error('获取日统计失败:', error)
				// 设置默认值以防止显示错误
				this.dailyStats = {
					totalRecords: 0,
					totalEarnings: 0,
					teaRecords: 0,
					teaEarnings: 0,
					totalWeight: 0,
					hourlyRecords: 0,
					hourlyEarnings: 0,
					totalHours: 0
				}
			}
		},

		// 计算单条采茶记录的统计数据（支持细粒度筛选）
		calculateTeaStatsForRecord(record) {
			const teaDetails = this.getTeaDetailsArray(record)
			if (!teaDetails || teaDetails.length === 0) {
				return { earnings: 0, weight: 0, hasMatchingDetails: false }
			}

			// 如果没有采茶筛选条件，返回整条记录的统计
			const hasTimePeriodFilter = this.filters.selectedTimePeriods && this.filters.selectedTimePeriods.length > 0
			const hasProjectFilter = this.filters.selectedProjects && this.filters.selectedProjects.length > 0

			if (!hasTimePeriodFilter && !hasProjectFilter) {
				const totalEarnings = this.safeParseFloat(record.total_earnings)
				const totalWeight = this.safeParseFloat(this.getTeaActualWeight(record))
				return {
					earnings: totalEarnings,
					weight: totalWeight,
					hasMatchingDetails: true
				}
			}

			// 有筛选条件时，只计算符合条件的详情项
			let filteredEarnings = 0
			let filteredWeight = 0
			let hasMatchingDetails = false

			teaDetails.forEach(detail => {
				let matchesFilter = true

				// 检查时间段筛选
				if (hasTimePeriodFilter) {
					matchesFilter = matchesFilter && this.filters.selectedTimePeriods.includes(detail.time_period)
				}

				// 检查项目类型筛选
				if (hasProjectFilter) {
					matchesFilter = matchesFilter && this.filters.selectedProjects.includes(detail.project)
				}

				if (matchesFilter) {
					hasMatchingDetails = true
					// 尝试多个可能的收入字段名
					const earnings = this.getDetailEarnings(detail)
					const weight = this.safeParseFloat(detail.actual_weight)

					filteredEarnings += earnings
					filteredWeight += weight
				}
			})

			return {
				earnings: filteredEarnings,
				weight: filteredWeight,
				hasMatchingDetails
			}
		},

		// 安全的数值解析方法
		safeParseFloat(value) {
			if (value === null || value === undefined || value === '') {
				return 0
			}

			const parsed = parseFloat(value)
			return isNaN(parsed) ? 0 : parsed
		},

		// 获取详情项的收入
		getDetailEarnings(detail) {
			// 尝试多个可能的收入字段名
			const possibleFields = ['earnings', 'total_earnings', 'income', 'amount']

			for (const field of possibleFields) {
				if (detail[field] !== undefined && detail[field] !== null) {
					const value = this.safeParseFloat(detail[field])
					if (value > 0) {
						return value
					}
				}
			}

			// 如果没有找到收入字段，尝试计算：实际重量 × 单价
			const actualWeight = this.safeParseFloat(detail.actual_weight)
			const unitPrice = this.safeParseFloat(detail.unit_price)

			if (actualWeight > 0 && unitPrice > 0) {
				return actualWeight * unitPrice
			}

			return 0
		},

		onDailyStatClick() {
			// 日统计卡片点击事件，可以滚动到记录列表或其他操作
		},

		async loadRecords() {
			this.loading = true
			try {
				console.log('=== 开始加载记录 ===')
				await this.fetchRecords()
				console.log('=== 记录加载完成 ===')
				console.log('当前记录数量:', this.records.length)

				// 检查采茶记录的数据结构
				const teaRecords = this.records.filter(r => r.work_mode === 'tea_picking')
				console.log('采茶记录数量:', teaRecords.length)

				teaRecords.forEach((record, index) => {
					console.log(`采茶记录 ${index + 1}:`, {
						id: record.id,
						worker_name: record.worker_name,
						work_mode: record.work_mode,
						tea_picking_details: record.tea_picking_details,
						tea_picking_details_type: typeof record.tea_picking_details,
						tea_picking_details_isArray: Array.isArray(record.tea_picking_details)
					})
				})
			} catch (error) {
				console.error('加载记录失败:', error)
				this.$showToast('加载失败', 'error')
			} finally {
				this.loading = false
			}
		},

		async refreshRecords() {
			await this.setFilters({ ...this.filters, page: 1 })
			await this.loadRecords()
			this.fetchDailyStats()
		},

		async loadMoreRecords() {
			if (this.pagination.page >= this.totalPages) return

			await this.loadPage(this.pagination.page + 1)
		},

		async loadPage(page) {
			await this.setFilters({ ...this.filters, page })
			await this.loadRecords()
		},

		onSearchInput() {
			// 防抖搜索
			clearTimeout(this.searchTimer)
			this.searchTimer = setTimeout(() => {
				this.setFilters({ ...this.filters, name: this.searchKeyword, page: 1 })
				this.loadRecords()
				this.fetchDailyStats()
			}, 500)
		},

		onSearchFocus() {
			// 搜索框聚焦时的处理
			this.searchFocused = true
		},

		onSearchBlur() {
			// 搜索框失焦时的处理
			this.searchFocused = false
		},

		clearSearch() {
			this.searchKeyword = ''
			this.setFilters({ ...this.filters, name: '', page: 1 })
			this.loadRecords()
			this.fetchDailyStats()
		},

		setWorkModeFilter(mode) {
			this.setFilters({ ...this.filters, workMode: mode, page: 1 })
			this.loadRecords()
			this.fetchDailyStats()
		},

		viewRecord(record) {
			console.log('查看记录详情:', record)

			// 根据工作模式跳转到对应的详情页面
			let detailUrl = ''
			if (record.work_mode === 'tea_picking') {
				detailUrl = `/pages/record/detail-tea?id=${record.id}`
			} else if (record.work_mode === 'hourly') {
				detailUrl = `/pages/record/detail-hourly?id=${record.id}`
			} else {
				uni.showToast({
					title: '未知的工作模式',
					icon: 'error'
				})
				return
			}

			uni.navigateTo({
				url: detailUrl,
				fail: function (err) {
					console.error('跳转到详情页面失败:', err)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'error'
					})
				}
			})
		},



		/**
		 * 删除工作记录
		 * @param {Object} record 工作记录对象
		 * @description 删除工作记录，包含级联删除关联的销售记录和事件通知机制
		 */
		async deleteRecord(record) {
			// 🔧 调试：添加详细的调试信息
			console.log('🗑️ [调试] deleteRecord方法被调用')
			console.log('🗑️ [调试] 传入的record参数:', record)
			console.log('🗑️ [调试] isAdmin状态:', this.isAdmin)
			console.log('🗑️ [调试] $salesManager实例:', this.$salesManager)

			// 🔧 调试：检查必要的依赖
			if (!record) {
				console.error('🗑️ [错误] record参数为空')
				uni.showToast({
					title: '参数错误：记录为空',
					icon: 'error',
					duration: 3000
				})
				return
			}

			if (!this.isAdmin) {
				console.error('🗑️ [错误] 用户权限不足，isAdmin:', this.isAdmin)
				uni.showToast({
					title: '权限不足，只有管理员可以删除记录',
					icon: 'error',
					duration: 3000
				})
				return
			}

			if (!this.$salesManager) {
				console.error('🗑️ [错误] SalesManager实例未初始化，尝试重新创建')
				// 🔧 修复：重新创建SalesManager实例
				try {
					this.$salesManager = new SalesManager()
					console.log('🗑️ [修复] SalesManager实例已重新创建:', this.$salesManager)
				} catch (error) {
					console.error('🗑️ [错误] 无法创建SalesManager实例:', error)
					uni.showToast({
						title: '系统错误：无法初始化销售管理器',
						icon: 'error',
						duration: 3000
					})
					return
				}
			}

			try {
				console.log('🗑️ [工作记录删除] 开始删除记录:', {
					id: record.id,
					worker_name: record.worker_name,
					date: record.date,
					work_mode: record.work_mode
				})

				// 🔧 检查是否有关联的销售记录
				const salesManager = this.$salesManager
				console.log('🗑️ [工作记录删除] 使用的salesManager实例:', salesManager)

				let relatedSalesRecords = []
				try {
					relatedSalesRecords = salesManager.salesDB.getSalesRecordsByDate(record.date)
					console.log('🗑️ [工作记录删除] 关联销售记录数量:', relatedSalesRecords.length)
				} catch (error) {
					console.error('🗑️ [工作记录删除] 获取关联销售记录失败:', error)
					// 如果获取失败，假设没有关联记录，继续删除流程
					relatedSalesRecords = []
				}

				// 构建详细的确认信息
				const modeText = this.getModeText(record.work_mode)
				const dateText = this.$formatDate(record.date)
				const earningsText = `¥${this.$formatCurrency(record.total_earnings)}`

				// 🔧 改进：添加更详细的记录信息
				let content = `确定要删除以下工作记录吗？

📋 基本信息：
工人：${record.worker_name}
日期：${dateText}
类型：${modeText}
工钱：${earningsText}`

				// 🔧 改进：添加工作详情信息
				if (record.work_mode === 'tea_picking') {
					const actualWeight = this.getTeaActualWeight(record)
					const avgPrice = this.getTeaAveragePrice(record)
					content += `
实际产量：${actualWeight}斤
平均单价：¥${avgPrice}/斤`
				} else if (record.work_mode === 'hourly') {
					const totalHours = this.getHourlyTotalHours(record)
					const avgRate = this.getHourlyAverageRate(record)
					content += `
工作时长：${totalHours}小时
平均时薪：¥${avgRate}/时`
				}

				// 🔧 改进：如果有关联的销售记录，添加更详细的警告信息
				if (relatedSalesRecords.length > 0) {
					const customerNames = relatedSalesRecords.map(sr => sr.customer_name || '未命名客户').join('、')
					const totalSalesAmount = relatedSalesRecords.reduce((sum, sr) => sum + (sr.selling_price * sr.production || 0), 0)

					content += `

⚠️ 级联删除警告：
该日期存在 ${relatedSalesRecords.length} 条销售记录
涉及客户：${customerNames}
销售总额：¥${this.$formatCurrency(totalSalesAmount)}

删除工作记录将同时删除所有相关的销售记录，
因为销售记录依赖于工作记录的产量数据。`
				}

				content += `

⚠️ 此操作不可撤销，请谨慎操作！`

				uni.showModal({
					title: '⚠️ 删除确认',
					content: content,
					confirmText: '确认删除',
					cancelText: '取消',
					confirmColor: '#f44336',
					success: async (res) => {
						if (res.confirm) {
							try {
								// 🔧 先删除关联的销售记录（级联删除）
								let cascadeDeleteResult = null
								if (relatedSalesRecords.length > 0) {
									console.log('🗑️ [工作记录删除] 开始级联删除销售记录')
									cascadeDeleteResult = await salesManager.deleteSalesRecordsByDate(record.date)

									if (!cascadeDeleteResult.success) {
										console.error('🗑️ [工作记录删除] 级联删除销售记录失败:', cascadeDeleteResult.message)
										uni.showToast({
											title: '删除关联销售记录失败',
											icon: 'error',
											duration: 3000
										})
										return
									}
									console.log('✅ [工作记录删除] 级联删除销售记录成功')
								}

								// 删除工作记录
								const result = await this.$store.dispatch('record/deleteRecord', record.id)

								if (result.success) {
									let successMessage = '删除成功'
									if (cascadeDeleteResult && cascadeDeleteResult.data.length > 0) {
										successMessage += `，同时删除了${cascadeDeleteResult.data.length}条销售记录`
									}

									uni.showToast({
										title: successMessage,
										icon: 'success',
										duration: 2000
									})

									// 🔧 触发收入页面数据更新事件
									console.log('📡 [工作记录] 准备发送updateIncomeRecord事件')
									console.log('📡 [工作记录] 删除的记录:', {
										id: record.id,
										date: record.date,
										worker_name: record.worker_name,
										cascadeDeleted: cascadeDeleteResult ? cascadeDeleteResult.data.length : 0
									})

									uni.$emit('updateIncomeRecord', {
										type: 'workRecordDeleted',
										deletedRecord: record,
										cascadeDeletedSalesRecords: cascadeDeleteResult ? cascadeDeleteResult.data : [],
										timestamp: new Date().toISOString()
									})

									console.log('📡 [工作记录] updateIncomeRecord事件已发送')

									// 延迟刷新，让用户看到成功提示
									setTimeout(() => {
										this.refreshRecords()
									}, 500)
								} else {
									uni.showToast({
										title: result.message || '删除失败',
										icon: 'error',
										duration: 3000
									})
								}
							} catch (error) {
								console.error('🗑️ [工作记录删除] 删除操作失败:', error)
								uni.showToast({
									title: '删除失败，请重试',
									icon: 'error',
									duration: 3000
								})
							}
						}
					}
				})

			} catch (error) {
				console.error('🗑️ [工作记录删除] 删除记录方法执行失败:', error)
				uni.showToast({
					title: '操作失败，请重试',
					icon: 'error',
					duration: 3000
				})
			}
		},

		// 🔧 调试：测试删除功能
		testDeleteFunction(record) {
			console.log('🧪 [测试] testDeleteFunction被调用')
			console.log('🧪 [测试] 当前用户状态:', {
				isAdmin: this.isAdmin,
				userInfo: this.$store.getters['user/userInfo'],
				userType: this.$store.getters['user/userType']
			})
			console.log('🧪 [测试] 传入的记录:', record)

			uni.showModal({
				title: '🧪 删除功能测试',
				content: `测试删除功能是否正常工作：

记录ID: ${record.id}
工人: ${record.worker_name}
日期: ${record.date}
管理员权限: ${this.isAdmin ? '是' : '否'}
SalesManager状态: ${this.$salesManager ? '已初始化' : '未初始化'}

选择测试方式：`,
				showCancel: true,
				confirmText: '完整删除',
				cancelText: '简化删除',
				success: (res) => {
					if (res.confirm) {
						console.log('🧪 [测试] 用户选择完整删除方法')
						this.deleteRecord(record)
					} else {
						console.log('🧪 [测试] 用户选择简化删除方法')
						this.simpleDeleteRecord(record)
					}
				}
			})
		},

		// 🔧 新增：简化版删除方法（不依赖SalesManager）
		async simpleDeleteRecord(record) {
			console.log('🗑️ [简化删除] 开始简化删除流程')

			const modeText = this.getModeText(record.work_mode)
			const dateText = this.$formatDate(record.date)
			const earningsText = `¥${this.$formatCurrency(record.total_earnings)}`

			let content = `确定要删除以下工作记录吗？

📋 基本信息：
工人：${record.worker_name}
日期：${dateText}
类型：${modeText}
工钱：${earningsText}

⚠️ 注意：这是简化删除模式，不会检查关联的销售记录。
⚠️ 此操作不可撤销，请谨慎操作！`

			uni.showModal({
				title: '⚠️ 简化删除确认',
				content: content,
				confirmText: '确认删除',
				cancelText: '取消',
				confirmColor: '#f44336',
				success: async (res) => {
					if (res.confirm) {
						try {
							console.log('🗑️ [简化删除] 开始删除工作记录')

							// 直接删除工作记录，不处理销售记录
							const result = await this.$store.dispatch('record/deleteRecord', record.id)

							if (result.success) {
								uni.showToast({
									title: '删除成功',
									icon: 'success',
									duration: 2000
								})

								// 发送更新事件
								uni.$emit('updateIncomeRecord', {
									type: 'workRecordDeleted',
									deletedRecord: record,
									cascadeDeletedSalesRecords: [],
									timestamp: new Date().toISOString()
								})

								// 刷新列表
								setTimeout(() => {
									this.refreshRecords()
								}, 500)
							} else {
								uni.showToast({
									title: result.message || '删除失败',
									icon: 'error',
									duration: 3000
								})
							}
						} catch (error) {
							console.error('🗑️ [简化删除] 删除失败:', error)
							uni.showToast({
								title: '删除失败，请重试',
								icon: 'error',
								duration: 3000
							})
						}
					}
				}
			})
		},

		// 🔧 调试：检查用户状态
		checkUserStatus() {
			console.log('👤 [用户状态检查] 开始检查用户状态')

			const userInfo = this.$store.getters['user/userInfo']
			const isLoggedIn = this.$store.getters['user/isLoggedIn']
			const userType = this.$store.getters['user/userType']
			const isAdmin = this.$store.getters['user/isAdmin']

			console.log('👤 [用户状态检查] 详细信息:', {
				userInfo,
				isLoggedIn,
				userType,
				isAdmin,
				computedIsAdmin: this.isAdmin
			})

			// 检查本地存储的用户信息
			const localUserInfo = uni.getStorageSync('userInfo')
			const localToken = uni.getStorageSync('token')

			console.log('👤 [用户状态检查] 本地存储:', {
				localUserInfo,
				localToken
			})

			// 如果用户未登录，提示登录
			if (!isLoggedIn || !userInfo) {
				console.warn('👤 [用户状态检查] 用户未登录，跳转到登录页面')
				uni.showModal({
					title: '未登录',
					content: '请先登录后再使用此功能',
					showCancel: false,
					success: () => {
						uni.reLaunch({
							url: '/pages/login/login'
						})
					}
				})
				return
			}

			// 如果不是管理员，显示权限提示
			if (!isAdmin) {
				console.log('👤 [用户状态检查] 当前用户不是管理员，删除按钮将被隐藏')
			} else {
				console.log('👤 [用户状态检查] 当前用户是管理员，删除按钮将显示')
			}
		},

		goToAddRecord() {
			uni.navigateTo({
				url: '/pages/record/add'
			})
		},



		// 打印记录列表
		async printRecordList() {
			try {
				if (this.records.length === 0) {
					this.$showToast('没有可打印的记录', 'none')
					return
				}

				this.$showLoading('准备打印...')
				const title = `工作记录列表_${new Date().toLocaleDateString()}`
				await printManager.printRecordList(this.records, title)
				this.$showToast('打印任务已发送', 'success')
			} catch (error) {
				console.error('打印失败:', error)
				this.$showToast('打印失败', 'error')
			} finally {
				this.$hideLoading()
			}
		},

		// 导出记录列表
		async exportRecordList() {
			try {
				// 检查是否有数据可导出
				if (!this.records || this.records.length === 0) {
					this.$showToast('暂无数据可导出', 'none')
					return
				}

				// 设置导出状态
				this.exporting = true
				this.$showLoading('正在导出...')

				// 获取当前筛选的所有记录（不仅仅是当前页）
				const allFilteredRecords = await this.getAllFilteredRecords()

				// 检测平台
				const platform = uni.getSystemInfoSync().platform === 'devtools' ? 'h5' :
					(uni.getSystemInfoSync().uniPlatform === 'web' ? 'h5' : 'app')

				// 使用导出工具导出
				const result = await recordExporter.exportToExcel(allFilteredRecords, platform)

				if (result.success) {
					this.$showToast('导出成功', 'success')
				} else {
					throw new Error(result.message || '导出失败')
				}

			} catch (error) {
				console.error('导出失败:', error)
				this.$showToast(error.message || '导出失败，请稍后重试', 'error')
			} finally {
				this.exporting = false
				this.$hideLoading()
			}
		},

		// 获取所有符合筛选条件的记录
		async getAllFilteredRecords() {
			try {
				// 获取所有记录
				const allRecords = uni.getStorageSync('workRecords') || []

				// 应用当前的筛选条件
				let filteredRecords = allRecords

				// 应用日期范围筛选
				if (this.filters.dateRange && this.filters.dateRange.length > 0) {
					const startDate = this.filters.dateRange[0]
					const endDate = this.filters.dateRange.length > 1 ? this.filters.dateRange[1] : startDate

					filteredRecords = filteredRecords.filter(record => {
						const recordDate = new Date(record.date).toISOString().split('T')[0]
						return recordDate >= startDate && recordDate <= endDate
					})
				}

				// 应用姓名筛选
				if (this.filters.name) {
					filteredRecords = filteredRecords.filter(record =>
						record.worker_name.includes(this.filters.name)
					)
				}

				// 应用工作模式筛选
				if (this.filters.workMode !== 'all') {
					filteredRecords = filteredRecords.filter(record =>
						record.work_mode === this.filters.workMode
					)
				}

				// 应用采茶模式的多选筛选
				if (this.filters.workMode === 'tea_picking') {
					// 时间段多选筛选
					if (this.filters.selectedTimePeriods && this.filters.selectedTimePeriods.length > 0) {
						filteredRecords = filteredRecords.filter(record => {
							if (record.work_mode !== 'tea_picking' || !record.tea_picking_details) {
								return false
							}

							const teaDetails = this.getTeaDetailsArray(record)
							return teaDetails.some(detail =>
								this.filters.selectedTimePeriods.includes(detail.time_period)
							)
						})
					}

					// 项目类型多选筛选
					if (this.filters.selectedProjects && this.filters.selectedProjects.length > 0) {
						filteredRecords = filteredRecords.filter(record => {
							if (record.work_mode !== 'tea_picking' || !record.tea_picking_details) {
								return false
							}

							const teaDetails = this.getTeaDetailsArray(record)
							return teaDetails.some(detail =>
								this.filters.selectedProjects.includes(detail.project)
							)
						})
					}
				}

				console.log('导出数据统计:', {
					总记录数: allRecords.length,
					筛选后记录数: filteredRecords.length,
					采茶记录数: filteredRecords.filter(r => r.work_mode === 'tea_picking').length,
					时工记录数: filteredRecords.filter(r => r.work_mode === 'hourly').length
				})

				return filteredRecords

			} catch (error) {
				console.error('获取筛选记录失败:', error)
				throw new Error('获取数据失败')
			}
		},

		// 转换数据为CSV格式
		convertToCSV(data) {
			if (!data || data.length === 0) return ''

			const headers = Object.keys(data[0])
			const csvRows = []

			// 添加表头
			csvRows.push(headers.join(','))

			// 添加数据行
			for (const row of data) {
				const values = headers.map(header => {
					const value = row[header]
					// 处理包含逗号的值，用双引号包围
					return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
				})
				csvRows.push(values.join(','))
			}

			return csvRows.join('\n')
		},

		// 筛选相关方法
		onStartDateChange(e) {
			this.tempFilters.startDate = e.detail.value
		},

		onEndDateChange(e) {
			this.tempFilters.endDate = e.detail.value
		},

		onTimePeriodChange(e) {
			this.tempFilters.timePeriodIndex = e.detail.value
		},

		onProjectChange(e) {
			this.tempFilters.projectIndex = e.detail.value
		},

		resetFilters() {
			this.tempFilters = {
				startDate: '',
				endDate: '',
				timePeriodIndex: 0,
				projectIndex: 0,
				selectedTimePeriods: [],
				selectedProjects: []
			}

			// 同时重置store中的筛选条件
			const newFilters = {
				...this.filters,
				selectedTimePeriods: [],
				selectedProjects: [],
				dateRange: []
			}
			this.setFilters(newFilters)
		},

		// 时间段多选方法
		toggleTimePeriod(period) {
			const index = this.tempFilters.selectedTimePeriods.indexOf(period)
			if (index > -1) {
				this.tempFilters.selectedTimePeriods.splice(index, 1)
			} else {
				this.tempFilters.selectedTimePeriods.push(period)
			}
		},

		selectAllTimePeriods() {
			this.tempFilters.selectedTimePeriods = ['morning', 'afternoon']
		},

		clearTimePeriods() {
			this.tempFilters.selectedTimePeriods = []
		},

		// 项目类型多选方法
		toggleProject(project) {
			const index = this.tempFilters.selectedProjects.indexOf(project)
			if (index > -1) {
				this.tempFilters.selectedProjects.splice(index, 1)
			} else {
				this.tempFilters.selectedProjects.push(project)
			}
		},

		selectAllProjects() {
			this.tempFilters.selectedProjects = ['one', 'two', 'three']
		},

		clearProjects() {
			this.tempFilters.selectedProjects = []
		},

		applyFilters() {
			const newFilters = { ...this.filters }

			// 应用日期范围
			if (this.tempFilters.startDate && this.tempFilters.endDate) {
				newFilters.dateRange = [this.tempFilters.startDate, this.tempFilters.endDate]
			} else {
				newFilters.dateRange = []
			}

			// 应用采茶模式多选筛选
			if (this.filters.workMode === 'tea_picking') {
				// 时间段多选筛选
				if (this.tempFilters.selectedTimePeriods.length > 0) {
					newFilters.selectedTimePeriods = [...this.tempFilters.selectedTimePeriods]
				} else {
					newFilters.selectedTimePeriods = []
				}

				// 项目类型多选筛选
				if (this.tempFilters.selectedProjects.length > 0) {
					newFilters.selectedProjects = [...this.tempFilters.selectedProjects]
				} else {
					newFilters.selectedProjects = []
				}
			}

			newFilters.page = 1
			this.setFilters(newFilters)
			this.loadRecords()
			this.fetchDailyStats()
			this.showFilterModal = false
		},

		// 辅助方法
		getModeText(mode) {
			return mode === 'tea_picking' ? '采茶' : '时工'
		},

		// 辅助方法：获取采茶详情数组
		getTeaDetailsArray(record) {
			if (!record.tea_picking_details) {
				return []
			}

			// 如果tea_picking_details已经是数组，直接返回
			if (Array.isArray(record.tea_picking_details)) {
				return record.tea_picking_details
			}

			// 如果tea_picking_details是对象，检查是否包含数组字段
			if (typeof record.tea_picking_details === 'object') {
				// 检查是否有tea_picking_details字段（嵌套结构）
				if (record.tea_picking_details.tea_picking_details && Array.isArray(record.tea_picking_details.tea_picking_details)) {
					return record.tea_picking_details.tea_picking_details
				}

				// 检查是否是类数组对象（有数字键的对象）
				const keys = Object.keys(record.tea_picking_details)
				const numericKeys = keys.filter(key => /^\d+$/.test(key)).sort((a, b) => parseInt(a) - parseInt(b))

				if (numericKeys.length > 0) {
					// 将类数组对象转换为真正的数组
					return numericKeys.map(key => record.tea_picking_details[key])
				}

				// 如果对象本身就是一个详情记录，包装成数组
				if (record.tea_picking_details.project || record.tea_picking_details.original_weight) {
					return [record.tea_picking_details]
				}
			}

			return []
		},

		// 采茶模式数据处理方法
		getTeaProjectSummary(record) {
			// 获取采茶详情数组
			let teaDetails = this.getTeaDetailsArray(record)

			if (!teaDetails || teaDetails.length === 0) {
				return '未知项目'
			}

			// 检查是否有筛选条件
			const hasTimePeriodFilter = this.filters.selectedTimePeriods && this.filters.selectedTimePeriods.length > 0
			const hasProjectFilter = this.filters.selectedProjects && this.filters.selectedProjects.length > 0

			// 统计各项目类型，区分是否符合筛选条件
			const projectCounts = {}
			const matchingProjectCounts = {}

			teaDetails.forEach(detail => {
				const project = this.getProjectText(detail.project)
				projectCounts[project] = (projectCounts[project] || 0) + 1

				// 检查是否符合筛选条件
				let matchesFilter = true
				if (hasTimePeriodFilter) {
					matchesFilter = matchesFilter && this.filters.selectedTimePeriods.includes(detail.time_period)
				}
				if (hasProjectFilter) {
					matchesFilter = matchesFilter && this.filters.selectedProjects.includes(detail.project)
				}

				if (matchesFilter) {
					matchingProjectCounts[project] = (matchingProjectCounts[project] || 0) + 1
				}
			})

			// 如果没有筛选条件，显示所有项目
			if (!hasTimePeriodFilter && !hasProjectFilter) {
				const summary = Object.entries(projectCounts)
					.map(([project, count]) => count > 1 ? `${project}×${count}` : project)
					.join('、')
				return summary || '未知项目'
			}

			// 有筛选条件时，突出显示符合条件的项目
			const allProjects = Object.entries(projectCounts)
				.map(([project, count]) => {
					const matchingCount = matchingProjectCounts[project] || 0
					if (matchingCount > 0) {
						// 符合筛选条件的项目用特殊标记
						const displayText = matchingCount > 1 ? `${project}×${matchingCount}` : project
						return `✓${displayText}`
					} else {
						// 不符合条件的项目用灰色显示
						const displayText = count > 1 ? `${project}×${count}` : project
						return displayText
					}
				})

			return allProjects.join('、') || '未知项目'
		},

		// 获取原斤数总和
		getTeaOriginalWeight(record) {
			let teaDetails = this.getTeaDetailsArray(record)

			if (!teaDetails || teaDetails.length === 0) {
				return '0.00'
			}

			const totalOriginalWeight = teaDetails.reduce((sum, detail) => {
				return sum + (parseFloat(detail.original_weight) || 0)
			}, 0)

			return totalOriginalWeight.toFixed(2)
		},

		// 获取实际斤数总和
		getTeaActualWeight(record) {
			let teaDetails = this.getTeaDetailsArray(record)

			if (!teaDetails || teaDetails.length === 0) {
				return '0.00'
			}

			const totalActualWeight = teaDetails.reduce((sum, detail) => {
				return sum + (parseFloat(detail.actual_weight) || 0)
			}, 0)

			return totalActualWeight.toFixed(2)
		},

		getTeaAveragePrice(record) {
			let teaDetails = this.getTeaDetailsArray(record)

			if (!teaDetails || teaDetails.length === 0) {
				return '0.00'
			}

			// 使用总工钱除以实际斤数计算平均单价
			const totalEarnings = parseFloat(record.total_earnings) || 0
			const totalActualWeight = parseFloat(this.getTeaActualWeight(record))

			if (totalActualWeight === 0) {
				return '0.00'
			}

			return (totalEarnings / totalActualWeight).toFixed(2)
		},



		// 时工模式数据处理方法
		getHourlyTotalHours(record) {
			if (!record.hourly_work_details) {
				return '0.00'
			}

			return (parseFloat(record.hourly_work_details.total_hours) || 0).toFixed(2)
		},

		getHourlyAverageRate(record) {
			if (!record.hourly_work_details) {
				return '0.00'
			}

			return (parseFloat(record.hourly_work_details.average_rate) || 0).toFixed(2)
		},

		getHourlyWorkMode(record) {
			if (!record.hourly_work_details) {
				return ''
			}

			if (record.hourly_work_details.is_detail_mode) {
				const modes = []
				if (record.hourly_work_details.detail_mode) {
					const detail = record.hourly_work_details.detail_mode
					if (detail.morning && detail.morning.work_hours > 0) modes.push('上午')
					if (detail.afternoon && detail.afternoon.work_hours > 0) modes.push('下午')
					if (detail.overtime && detail.overtime.work_hours > 0) modes.push('加班')
				}
				return modes.length > 0 ? modes.join('、') : '详细模式'
			} else {
				return '简化模式'
			}
		},

		getTimePeriodText(period) {
			return period === 'morning' ? '上午' : '下午'
		},

		getProjectText(project) {
			const projectMap = { one: '一叶', two: '二叶', three: '三叶' }
			return projectMap[project] || project
		}
	}
}
</script>

<style scoped>
.record-list-page {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 100rpx;

}

/* 现代化日期显示区域 */
.date-display-container {
	margin: 0;
	padding-bottom: 20rpx;
}

.date-display-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
}

.date-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 20rpx;
}

.date-info-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.date-label {
	font-size: 26rpx;
	color: #999;
	font-weight: 500;
	letter-spacing: 0.5rpx;
}

.date-value {
	font-size: 34rpx;
	font-weight: 700;
	color: #333;
	letter-spacing: 0.5rpx;
}

.date-actions-section {
	flex-shrink: 0;
}

.back-to-today-btn {
	background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
	border: none;
	border-radius: 12rpx;
	padding: 0;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
	position: relative;
	box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.25);
	min-width: 80rpx;
}

.back-to-today-btn::after {
	border: none;
}

.back-to-today-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

.back-to-today-btn .btn-content {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	padding: 5rpx 20rpx;
	min-height: 62rpx;
}

.back-to-today-btn:active .btn-icon {
	transform: scale(1.1);
}

.back-to-today-btn .btn-text {
	font-size: 28rpx;
	font-weight: 600;
	color: white;
	letter-spacing: 0.5rpx;
}

/* 日期显示区域增强交互效果 */
@media (min-width: 751rpx) {
	.back-to-today-btn:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.35);
	}

	.back-to-today-btn:hover .btn-icon {
		transform: scale(1.05);
	}
}

/* 日期值动画效果 */
.date-value {
	position: relative;
	overflow: hidden;
}

.date-value::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.1), transparent);
	transition: left 0.8s ease;
}

.date-display-card:hover .date-value::before {
	left: 100%;
}

/* 按钮内部光泽效果 */
.back-to-today-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 50%;
	background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
	border-radius: 12rpx 12rpx 0 0;
	pointer-events: none;
}

/* 统一的操作按钮样式*/
.enhanced-action-section {
	margin: 0 0 30rpx;
}

.action-buttons-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
}

.action-buttons-grid {
	display: flex;
	gap: 16rpx;
	justify-content: space-between;
}

.enhanced-action-btn {
	flex: 1;
	background: white;
	border: none;
	border-radius: 20rpx;
	padding: 0;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
	position: relative;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.enhanced-action-btn::after {
	border: none;
}

.enhanced-action-btn:active {
	transform: scale(0.96);
}

.enhanced-action-btn.export-btn {
	background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
	box-shadow: 0 6rpx 24rpx rgba(76, 175, 80, 0.3);
}

.enhanced-action-btn.export-btn:active {
	box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.4);
}

.enhanced-action-btn.print-btn {
	background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
	box-shadow: 0 6rpx 24rpx rgba(33, 150, 243, 0.3);
}

.enhanced-action-btn.print-btn:active {
	box-shadow: 0 4rpx 16rpx rgba(33, 150, 243, 0.4);
}

.btn-content {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 24rpx 20rpx;
	min-height: 100rpx;
}

.btn-content .btn-icon {
	font-size: 40rpx;
	color: white;
	transition: transform 0.3s ease;
}

.enhanced-action-btn:active .btn-icon {
	transform: scale(1.1);
}

.btn-content .btn-text {
	font-size: 30rpx;
	font-weight: 700;
	color: white;
	letter-spacing: 0.5rpx;
}

/* 按钮悬停和交互效果 */
@media (min-width: 751rpx) {
	.enhanced-action-btn:hover {
		transform: translateY(-4rpx);
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
	}

	.enhanced-action-btn.export-btn:hover {
		box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.4);
	}

	.enhanced-action-btn.print-btn:hover {
		box-shadow: 0 8rpx 32rpx rgba(33, 150, 243, 0.4);
	}

	.enhanced-action-btn:hover .btn-icon {
		transform: scale(1.05);
	}
}

/* 按钮加载状态 */
.enhanced-action-btn.loading {
	opacity: 0.8;
	pointer-events: none;
}

.enhanced-action-btn.loading .btn-icon {
	animation: spin 1s linear infinite;
}

.enhanced-action-btn.loading::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.2);
	animation: pulse 1.5s ease-in-out infinite;
	border-radius: 20rpx;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

@keyframes pulse {

	0%,
	100% {
		opacity: 0;
	}

	50% {
		opacity: 1;
	}
}

/* 按钮焦点状态 */
.enhanced-action-btn:focus {
	outline: none;
	box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.3);
}

.enhanced-action-btn.print-btn:focus {
	box-shadow: 0 0 0 4rpx rgba(33, 150, 243, 0.3);
}

/* 按钮内部光泽效果 */
.enhanced-action-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 50%;
	background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
	border-radius: 20rpx 20rpx 0 0;
	pointer-events: none;
}

/* 按钮禁用状态 */
.enhanced-action-btn:disabled {
	opacity: 0.5;
	pointer-events: none;
	background: #f5f5f5;
	box-shadow: none;
}

.enhanced-action-btn:disabled .btn-icon,
.enhanced-action-btn:disabled .btn-text {
	color: #999;
}

/* 现代化搜索和筛选区域 */
.search-filter-container {
	margin: 0 20rpx 3rpx;
}

.search-filter-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
}

.search-bar {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 20rpx;
}

.search-input-wrapper {
	flex: 1;
	display: flex;
	align-items: center;
	background: #f8faf9;
	border: 2rpx solid #e8f0e8;
	border-radius: 12rpx;
	padding: 0 16rpx;
	transition: all 0.3s ease;
	position: relative;
}

.search-input-wrapper:focus-within {
	border-color: #4caf50;
	background: white;
	box-shadow: 0 0 0 3rpx rgba(76, 175, 80, 0.1);
}

.search-icon {
	font-size: 28rpx;
	color: #999;
	margin-right: 12rpx;
	transition: color 0.3s ease;
}

.search-input-wrapper:focus-within .search-icon {
	color: #4caf50;
}

.search-input {
	flex: 1;
	padding: 16rpx 0;
	font-size: 30rpx;
	background: transparent;
	border: none;
	outline: none;
	color: #333;
}

.search-input::placeholder {
	color: #999;
}

.clear-search-btn {
	font-size: 24rpx;
	color: #999;
	padding: 8rpx;
	margin-left: 8rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	border-radius: 50%;
	width: 32rpx;
	height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.clear-search-btn:active {
	background: #f0f0f0;
	color: #666;
	transform: scale(0.9);
}

.filter-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 16rpx 20rpx;
	background: #f8faf9;
	border: 2rpx solid #e8f0e8;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: #333;
	transition: all 0.3s ease;
	position: relative;
	min-width: 120rpx;
	justify-content: center;
}

.filter-btn::after {
	border: none;
}

.filter-btn:active {
	background: #f0f9f0;
	border-color: #4caf50;
	transform: scale(0.95);
}

.filter-icon {
	font-size: 24rpx;
	color: #666;
}

.filter-text {
	font-size: 28rpx;
	font-weight: 500;
}

.filter-badge {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	background: linear-gradient(135deg, #ff4444 0%, #cc0000 100%);
	color: white;
	font-size: 20rpx;
	font-weight: 700;
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	min-width: 24rpx;
	height: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(255, 68, 68, 0.3);
}

.quick-filters {
	margin-top: 4rpx;
}

.filter-tabs {
	display: flex;
	background: #f8faf9;
	border-radius: 12rpx;
	padding: 6rpx;
	gap: 4rpx;
}

.filter-tab {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	padding: 16rpx 12rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #666;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	position: relative;
}

.filter-tab:active {
	transform: scale(0.95);
}

.filter-tab.active {
	background: white;
	color: #2e7d32;
	font-weight: 600;
	box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.15);
	border: 1rpx solid rgba(76, 175, 80, 0.2);
}

.tab-icon {
	font-size: 24rpx;
}

.tab-text {
	font-size: 28rpx;
}

/* 搜索筛选区域增强交互效果 */
@media (min-width: 751rpx) {
	.filter-btn:hover {
		background: #f0f9f0;
		border-color: #4caf50;
		transform: translateY(-2rpx);
		box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.2);
	}

	.filter-tab:hover:not(.active) {
		background: rgba(76, 175, 80, 0.05);
		color: #4caf50;
	}

	.clear-search-btn:hover {
		background: #f0f0f0;
		color: #666;
	}
}

/* 搜索框动画效果 */
.search-input-wrapper {
	overflow: hidden;
}

.search-input-wrapper::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.1), transparent);
	transition: left 0.6s ease;
}

.search-input-wrapper:focus-within::before {
	left: 100%;
}

/* 筛选标签切换动画 */
.filter-tab {
	overflow: hidden;
}

.filter-tab::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(46, 125, 50, 0.1) 100%);
	opacity: 0;
	transition: opacity 0.3s ease;
	border-radius: 8rpx;
}

.filter-tab.active::before {
	opacity: 1;
}

/* 筛选徽章脉冲动画 */
.filter-badge {
	animation: badge-pulse 2s ease-in-out infinite;
}

@keyframes badge-pulse {

	0%,
	100% {
		transform: scale(1);
		box-shadow: 0 2rpx 8rpx rgba(255, 68, 68, 0.3);
	}

	50% {
		transform: scale(1.05);
		box-shadow: 0 4rpx 16rpx rgba(255, 68, 68, 0.5);
	}
}

.record-list {
	padding: 20rpx;
}

.record-item {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	cursor: pointer;
	transition: all 0.3s ease;
}

.record-item:active {
	transform: scale(0.98);
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.worker-info {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.worker-name {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.work-mode-badge {
	padding: 6rpx 15rpx;
	border-radius: 15rpx;
	font-size: 22rpx;
	color: white;
}

.work-mode-badge.tea_picking {
	background-color: #2e7d32;
}

.work-mode-badge.hourly {
	background-color: #2196F3;
}

.record-actions {
	display: flex;
	gap: 15rpx;
}

.action-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	flex: 1;
	padding: 15rpx 0;
	font-size: 28rpx;
	color: #333;
	background-color: transparent;
	border: none;
	line-height: 1.5;
}

.action-btn::after {
	border: none;
}

.action-btn:first-child {
	border-right: 1rpx solid #eee;
}

.action-btn.edit {
	color: #2196F3;
}

.action-btn.delete {
	color: #f44336;
}

.record-content {
	margin-bottom: 20rpx;
}

.record-date {
	margin-bottom: 15rpx;
}

.date-text {
	font-size: 26rpx;
	color: #999;
}

.record-details {
	margin-top: 20rpx;
}

.detail-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 16rpx;
	gap: 20rpx;
}

.detail-row:last-child {
	margin-bottom: 0;
}

.detail-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	padding: 16rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	border-left: 4rpx solid #e9ecef;
	transition: all 0.3s ease;
}

.detail-item.full-width {
	flex: 1 1 100%;
}

.detail-item:hover {
	background-color: #f1f3f4;
	border-left-color: #2e7d32;
}

.detail-label {
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
	line-height: 1.2;
}

.detail-value {
	font-size: 30rpx;
	color: #333;
	font-weight: 600;
	line-height: 1.2;
}

.record-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 24rpx;
	padding: 20rpx;
	background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
	border-radius: 12rpx;
	border: 2rpx solid #bbf7d0;
	box-shadow: 0 2rpx 8rpx rgba(34, 197, 94, 0.1);
}

.earnings-label {
	font-size: 32rpx;
	color: #166534;
	font-weight: 600;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.earnings-label::before {
	content: '💰';
	font-size: 28rpx;
}

.earnings-value {
	font-size: 40rpx;
	font-weight: 700;
	color: #15803d;
	text-shadow: 0 1rpx 2rpx rgba(21, 128, 61, 0.1);
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 100rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.5;
}

.empty-text {
	font-size: 32rpx;
	color: #999;
	margin-bottom: 15rpx;
}

.empty-hint {
	font-size: 26rpx;
	color: #ccc;
}

.pagination {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 30rpx;
	padding: 30rpx;
	background-color: #fff;
	margin: 20rpx;
	border-radius: 10rpx;
}

.page-btn {
	padding: 15rpx 30rpx;
	background-color: #2e7d32;
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 26rpx;
}

.page-btn:disabled {
	background-color: #ccc;
}

.page-info {
	font-size: 28rpx;
	color: #666;
}

.fab {
	position: fixed;
	bottom: 120rpx;
	right: 30rpx;
	width: 120rpx;
	height: 120rpx;
	background-color: #2e7d32;
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 20rpx rgba(76, 175, 80, 0.4);
	cursor: pointer;
	transition: all 0.3s ease;
	z-index: 1001;
}

.fab-icon {
	font-size: 48rpx;
	color: white;
	font-weight: 300;
}

/* 筛选弹窗样式 */
.filter-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	z-index: 1000;
}

.filter-content {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	width: 100%;
	max-height: 80vh;
	overflow-y: auto;
}

.filter-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.filter-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.filter-close {
	font-size: 36rpx;
	color: #999;
	cursor: pointer;
}

.filter-body {
	padding: 30rpx;
}

.filter-group {
	margin-bottom: 40rpx;
}

.filter-group-title {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.date-range {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.date-picker {
	flex: 1;
	padding: 25rpx 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	text-align: center;
	font-size: 28rpx;
	color: #333;
}

.date-separator {
	font-size: 24rpx;
	color: #999;
}

.filter-options {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.filter-option {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.option-label {
	font-size: 28rpx;
	color: #666;
	min-width: 120rpx;
}

.option-picker {
	flex: 1;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
}

.filter-footer {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 2rpx solid #f0f0f0;
}

.filter-btn-secondary {
	flex: 1;
	padding: 25rpx;
	background-color: #f8f9fa;
	color: #666;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
}

.filter-btn-primary {
	flex: 1;
	padding: 25rpx;
	background-color: #2e7d32;
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
}

/* 多选筛选样式 */
.filter-section {
	margin-bottom: 30rpx;
}

.filter-section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.filter-section-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.filter-section-actions {
	display: flex;
	gap: 15rpx;
}

.filter-action-btn {
	font-size: 24rpx;
	color: #2e7d32;
	padding: 5rpx 10rpx;
	border: 1rpx solid #2e7d32;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.filter-action-btn:active {
	background-color: #2e7d32;
	color: white;
}

.filter-button-group {
	display: flex;
	gap: 12rpx;
	flex-wrap: wrap;
}

.filter-multi-btn {
	flex: 1;
	min-width: 120rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 16rpx;
	background-color: #f8f8f8;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	min-height: 100rpx;
}

.filter-multi-btn:hover {
	background-color: #f0f0f0;
	border-color: #d0d0d0;
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-multi-btn.active {
	background-color: #e8f5e8;
	border-color: #2e7d32;
	color: #2e7d32;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.2);
}

.filter-multi-btn.active:hover {
	background-color: #dcedc8;
	border-color: #388e3c;
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 16rpx rgba(76, 175, 80, 0.3);
}

.filter-multi-btn .btn-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
	display: block;
}

.filter-multi-btn .btn-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #666;
	display: block;
}

.filter-multi-btn.active .btn-text {
	color: #2e7d32;
	font-weight: 600;
}

/* 响应式设计 - 小屏幕适配 */
@media screen and (max-width: 750rpx) {
	.filter-button-group {
		gap: 8rpx;
	}

	.filter-multi-btn {
		padding: 16rpx 12rpx;
		min-height: 80rpx;
	}

	.filter-multi-btn .btn-icon {
		font-size: 28rpx;
		margin-bottom: 6rpx;
	}

	.filter-multi-btn .btn-text {
		font-size: 24rpx;
	}

	.filter-section-actions {
		gap: 10rpx;
	}

	.filter-action-btn {
		font-size: 22rpx;
		padding: 4rpx 8rpx;
	}

	/* 日期显示区域响应式 */
	.date-display-container {
		margin: 15rpx 15rpx 0;
	}

	.date-display-card {
		padding: 20rpx;
	}

	.date-content {
		flex-direction: column;
		align-items: flex-start;
		gap: 16rpx;
	}

	.date-info-section {
		width: 100%;
	}

	.date-actions-section {
		width: 100%;
		display: flex;
		justify-content: center;
	}

	.back-to-today-btn {
		width: 100%;
		min-width: auto;
	}

	.back-to-today-btn .btn-content {
		padding: 14rpx 16rpx;
		min-height: 64rpx;
	}

	.back-to-today-btn .btn-icon {
		font-size: 26rpx;
	}

	.back-to-today-btn .btn-text {
		font-size: 26rpx;
	}

	/* 搜索筛选区域响应式 */
	.search-filter-container {
		margin: 0 15rpx 15rpx;
	}

	.search-filter-card {
		padding: 20rpx;
	}

	.search-bar {
		flex-direction: column;
		gap: 12rpx;
		margin-bottom: 16rpx;
	}

	.search-input-wrapper {
		width: 100%;
	}

	.filter-btn {
		width: 100%;
		min-width: auto;
	}

	.filter-tabs {
		padding: 4rpx;
		gap: 2rpx;
	}

	.filter-tab {
		padding: 12rpx 8rpx;
		gap: 6rpx;
	}

	.tab-icon {
		font-size: 22rpx;
	}

	.tab-text {
		font-size: 26rpx;
	}

	/* 操作按钮响应式 */
	.enhanced-action-section {
		margin: 0 15rpx 25rpx;
	}

	.action-buttons-card {
		padding: 20rpx;
	}

	.action-buttons-grid {
		flex-direction: column;
		gap: 12rpx;
	}

	.enhanced-action-btn {
		width: 100%;
	}

	.btn-content {
		padding: 20rpx 16rpx;
		min-height: 80rpx;
		gap: 10rpx;
	}

	.btn-content .btn-icon {
		font-size: 36rpx;
	}

	.btn-content .btn-text {
		font-size: 28rpx;
	}
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {

	/* 记录详情响应式 */
	.detail-row {
		flex-direction: column;
		gap: 12rpx;
	}

	.detail-item {
		padding: 12rpx;
	}

	.detail-label {
		font-size: 24rpx;
	}

	.detail-value {
		font-size: 28rpx;
	}

	.earnings-label {
		font-size: 30rpx;
	}

	.earnings-value {
		font-size: 36rpx;
	}

	.record-footer {
		padding: 16rpx;
		margin-top: 20rpx;
	}

	.worker-name {
		font-size: 30rpx;
	}

	.date-text {
		font-size: 24rpx;
	}
}
</style>
