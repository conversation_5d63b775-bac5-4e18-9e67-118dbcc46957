/**
 * SalesManager 导入和初始化测试
 * 用于验证 SalesManager 类是否能正确导入和初始化
 */

// 测试SalesManager导入
console.log('🧪 [SalesManager测试] 开始测试SalesManager导入和初始化')

// 方法1：直接导入测试
try {
    console.log('🧪 [SalesManager测试] 尝试导入SalesManager...')
    
    // 在浏览器环境中模拟导入
    if (typeof require !== 'undefined') {
        // Node.js环境
        const SalesManager = require('../utils/salesManager.js').default || require('../utils/salesManager.js')
        console.log('✅ [SalesManager测试] Node.js环境导入成功:', SalesManager)
        
        // 测试实例化
        const salesManagerInstance = new SalesManager()
        console.log('✅ [SalesManager测试] 实例化成功:', salesManagerInstance)
        console.log('✅ [SalesManager测试] salesDB属性:', salesManagerInstance.salesDB)
        
    } else {
        // 浏览器环境 - 需要通过模块系统
        console.log('🧪 [SalesManager测试] 浏览器环境，需要通过ES6模块导入')
    }
    
} catch (error) {
    console.error('❌ [SalesManager测试] 导入失败:', error)
    console.error('❌ [SalesManager测试] 错误详情:', error.message)
    console.error('❌ [SalesManager测试] 错误堆栈:', error.stack)
}

// 方法2：在Vue组件中测试
const testInVueComponent = {
    name: 'SalesManagerTest',
    data() {
        return {
            salesManager: null,
            testResults: []
        }
    },
    
    async created() {
        await this.testSalesManagerImport()
    },
    
    methods: {
        async testSalesManagerImport() {
            console.log('🧪 [Vue组件测试] 开始在Vue组件中测试SalesManager')
            
            try {
                // 动态导入
                const SalesManagerModule = await import('@/utils/salesManager.js')
                const SalesManager = SalesManagerModule.default || SalesManagerModule
                
                console.log('✅ [Vue组件测试] 动态导入成功:', SalesManager)
                this.testResults.push('动态导入成功')
                
                // 实例化测试
                this.salesManager = new SalesManager()
                console.log('✅ [Vue组件测试] 实例化成功:', this.salesManager)
                this.testResults.push('实例化成功')
                
                // 属性测试
                if (this.salesManager.salesDB) {
                    console.log('✅ [Vue组件测试] salesDB属性存在:', this.salesManager.salesDB)
                    this.testResults.push('salesDB属性正常')
                } else {
                    console.error('❌ [Vue组件测试] salesDB属性不存在')
                    this.testResults.push('salesDB属性缺失')
                }
                
                // 方法测试
                if (typeof this.salesManager.salesDB.getSalesRecordsByDate === 'function') {
                    console.log('✅ [Vue组件测试] getSalesRecordsByDate方法存在')
                    this.testResults.push('getSalesRecordsByDate方法正常')
                    
                    // 测试调用
                    try {
                        const testRecords = this.salesManager.salesDB.getSalesRecordsByDate('2024-01-01')
                        console.log('✅ [Vue组件测试] 方法调用成功，返回:', testRecords)
                        this.testResults.push('方法调用成功')
                    } catch (callError) {
                        console.error('❌ [Vue组件测试] 方法调用失败:', callError)
                        this.testResults.push('方法调用失败: ' + callError.message)
                    }
                } else {
                    console.error('❌ [Vue组件测试] getSalesRecordsByDate方法不存在')
                    this.testResults.push('getSalesRecordsByDate方法缺失')
                }
                
            } catch (error) {
                console.error('❌ [Vue组件测试] 测试失败:', error)
                this.testResults.push('测试失败: ' + error.message)
            }
            
            // 输出测试结果
            console.log('🧪 [Vue组件测试] 测试结果汇总:', this.testResults)
        }
    }
}

// 方法3：在控制台中手动测试的函数
window.testSalesManager = async function() {
    console.log('🧪 [控制台测试] 开始手动测试SalesManager')
    
    try {
        // 检查是否已经有SalesManager实例
        const currentPage = getCurrentPages()[getCurrentPages().length - 1]
        if (currentPage && currentPage.$vm && currentPage.$vm.$salesManager) {
            console.log('✅ [控制台测试] 找到现有的SalesManager实例:', currentPage.$vm.$salesManager)
            return currentPage.$vm.$salesManager
        }
        
        // 尝试创建新实例
        if (typeof SalesManager !== 'undefined') {
            const instance = new SalesManager()
            console.log('✅ [控制台测试] 创建新实例成功:', instance)
            return instance
        } else {
            console.error('❌ [控制台测试] SalesManager类不可用')
            return null
        }
        
    } catch (error) {
        console.error('❌ [控制台测试] 测试失败:', error)
        return null
    }
}

// 方法4：检查依赖链
window.checkSalesManagerDependencies = function() {
    console.log('🧪 [依赖检查] 开始检查SalesManager依赖链')
    
    const dependencies = {
        SalesManager: typeof SalesManager !== 'undefined',
        SalesRecordDB: false,
        LocalDatabase: false
    }
    
    try {
        // 检查SalesRecordDB
        if (typeof require !== 'undefined') {
            const SalesRecordDB = require('@/utils/salesRecord.js').default || require('@/utils/salesRecord.js')
            dependencies.SalesRecordDB = !!SalesRecordDB
        }
    } catch (error) {
        console.warn('⚠️ [依赖检查] SalesRecordDB检查失败:', error.message)
    }
    
    try {
        // 检查LocalDatabase
        if (typeof require !== 'undefined') {
            const LocalDatabase = require('@/utils/database.js').default || require('@/utils/database.js')
            dependencies.LocalDatabase = !!LocalDatabase
        }
    } catch (error) {
        console.warn('⚠️ [依赖检查] LocalDatabase检查失败:', error.message)
    }
    
    console.log('🧪 [依赖检查] 依赖状态:', dependencies)
    return dependencies
}

// 方法5：修复SalesManager实例的函数
window.fixSalesManager = function() {
    console.log('🔧 [修复] 开始修复SalesManager实例')
    
    try {
        const currentPage = getCurrentPages()[getCurrentPages().length - 1]
        if (currentPage && currentPage.$vm) {
            // 尝试重新创建实例
            if (typeof SalesManager !== 'undefined') {
                currentPage.$vm.$salesManager = new SalesManager()
                console.log('✅ [修复] SalesManager实例已修复:', currentPage.$vm.$salesManager)
                return true
            } else {
                console.error('❌ [修复] SalesManager类不可用')
                return false
            }
        } else {
            console.error('❌ [修复] 无法访问当前页面实例')
            return false
        }
    } catch (error) {
        console.error('❌ [修复] 修复失败:', error)
        return false
    }
}

console.log('🧪 [SalesManager测试] 测试工具已加载，可用命令：')
console.log('- testSalesManager() - 测试SalesManager实例')
console.log('- checkSalesManagerDependencies() - 检查依赖')
console.log('- fixSalesManager() - 修复SalesManager实例')

export { testInVueComponent }
