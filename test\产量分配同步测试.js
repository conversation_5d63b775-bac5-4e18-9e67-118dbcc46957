/**
 * 产量分配同步功能测试脚本
 * 用于验证收入详情页面的产量分配同步修复效果
 */

// 模拟测试数据
const testData = {
    // 原始工作记录数据
    originalWorkRecord: {
        id: 'test_work_001',
        date: '2024-01-15',
        worker_name: '张三',
        work_mode: 'tea_picking',
        tea_picking_details: [
            {
                time_period: 'morning',
                actual_weight: 15.5,
                tea_type: '绿茶',
                price_per_kg: 25.0
            },
            {
                time_period: 'afternoon', 
                actual_weight: 12.3,
                tea_type: '绿茶',
                price_per_kg: 25.0
            }
        ],
        total_earnings: 695.0
    },

    // 修改后的工作记录数据
    updatedWorkRecord: {
        id: 'test_work_001',
        date: '2024-01-15',
        worker_name: '张三',
        work_mode: 'tea_picking',
        tea_picking_details: [
            {
                time_period: 'morning',
                actual_weight: 18.2, // 上午产量增加
                tea_type: '绿茶',
                price_per_kg: 25.0
            },
            {
                time_period: 'afternoon',
                actual_weight: 10.1, // 下午产量减少
                tea_type: '绿茶',
                price_per_kg: 25.0
            }
        ],
        total_earnings: 707.5
    },

    // 原始销售记录数据
    originalSalesRecords: [
        {
            id: 'test_sales_001',
            customer_name: '客户A',
            selling_price: 30.0,
            production: 15.5, // 对应原始上午产量
            date: '2024-01-15'
        },
        {
            id: 'test_sales_002', 
            customer_name: '客户B',
            selling_price: 28.0,
            production: 12.3, // 对应原始下午产量
            date: '2024-01-15'
        }
    ]
}

// 测试用例
const testCases = [
    {
        name: '单客户产量分配同步测试',
        description: '验证单客户场景下产量变化时的自动重新分配',
        setup: () => {
            return {
                salesMode: 'single_customer',
                salesRecords: [{
                    id: 'test_sales_001',
                    customer_name: '客户A',
                    selling_price: 30.0,
                    production: 27.8, // 原始总产量
                    date: '2024-01-15'
                }]
            }
        },
        expectedResult: {
            totalProduction: 28.3, // 新的总产量
            salesRecords: [{
                customer_name: '客户A',
                selling_price: 30.0, // 保持不变
                production: 28.3 // 自动调整为新的总产量
            }]
        }
    },

    {
        name: '双客户时间段分配同步测试',
        description: '验证双客户场景下时间段产量变化时的智能重新分配',
        setup: () => {
            return {
                salesMode: 'multiple_customers',
                salesRecords: [
                    {
                        id: 'test_sales_001',
                        customer_name: '客户A',
                        selling_price: 30.0,
                        production: 15.5, // 原始上午产量
                        date: '2024-01-15'
                    },
                    {
                        id: 'test_sales_002',
                        customer_name: '客户B', 
                        selling_price: 28.0,
                        production: 12.3, // 原始下午产量
                        date: '2024-01-15'
                    }
                ]
            }
        },
        expectedResult: {
            morningProduction: 18.2, // 新的上午产量
            afternoonProduction: 10.1, // 新的下午产量
            salesRecords: [
                {
                    customer_name: '客户A',
                    selling_price: 30.0, // 保持不变
                    production: 18.2 // 调整为新的上午产量
                },
                {
                    customer_name: '客户B',
                    selling_price: 28.0, // 保持不变
                    production: 10.1 // 调整为新的下午产量
                }
            ]
        }
    },

    {
        name: '产量减少场景测试',
        description: '验证产量减少时的分配调整逻辑',
        setup: () => {
            return {
                originalProduction: 27.8,
                newProduction: 25.5,
                salesRecords: [
                    {
                        customer_name: '客户A',
                        selling_price: 30.0,
                        production: 15.0
                    },
                    {
                        customer_name: '客户B',
                        selling_price: 28.0,
                        production: 12.8
                    }
                ]
            }
        },
        expectedResult: {
            totalAllocated: 25.5, // 应等于新的总产量
            adjustmentMade: true // 应该进行了调整
        }
    }
]

// 测试执行函数
function runTests() {
    console.log('🧪 开始执行产量分配同步测试')
    console.log('=' * 50)

    testCases.forEach((testCase, index) => {
        console.log(`\n📋 测试用例 ${index + 1}: ${testCase.name}`)
        console.log(`📝 描述: ${testCase.description}`)
        
        try {
            // 设置测试环境
            const setupData = testCase.setup()
            console.log('🔧 测试环境设置完成:', setupData)
            
            // 模拟产量变化事件
            const productionChangeEvent = {
                type: 'workRecordUpdated',
                updatedRecord: testData.updatedWorkRecord,
                productionChanged: true,
                originalProduction: 27.8,
                newProduction: 28.3,
                timestamp: new Date().toISOString()
            }
            
            console.log('📡 模拟产量变化事件:', productionChangeEvent)
            
            // 验证预期结果
            console.log('✅ 预期结果:', testCase.expectedResult)
            console.log(`✅ 测试用例 ${index + 1} 设置完成`)
            
        } catch (error) {
            console.error(`❌ 测试用例 ${index + 1} 执行失败:`, error)
        }
    })
    
    console.log('\n🎯 测试执行完成')
    console.log('\n📋 手动测试步骤:')
    console.log('1. 打开收入详情页面，设置客户信息和产量分配')
    console.log('2. 切换到工作记录编辑页面，修改产量数据')
    console.log('3. 返回收入详情页面，观察以下内容:')
    console.log('   - 总产量是否已更新')
    console.log('   - 产量分配是否自动重新计算')
    console.log('   - 客户信息和单价是否保持不变')
    console.log('   - 保存后数据是否正确')
    console.log('\n🔍 关键验证点:')
    console.log('- 产量分配总和应等于最新的总产量')
    console.log('- 客户名称和单价应保持用户原始输入')
    console.log('- 自动保存应正确更新数据库记录')
    console.log('- 用户应收到清晰的变化通知')
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testData,
        testCases,
        runTests
    }
} else {
    // 在浏览器环境中直接运行
    runTests()
}
