
.detail-container[data-v-724d1175] {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	/* 适配状态栏高度 - 多种兼容方案 */
	padding-top: constant(safe-area-inset-top);
	/* iOS 11.0-11.2 */
	padding-top: env(safe-area-inset-top);
	/* iOS 11.2+ */
	/* 备用方案 */
	padding-top: var(--status-bar-height, 0px);
}
.detail-container[data-v-724d1175]::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

/* 顶部导航栏样式 - 参考detail-hourly.vue的稳定布局 */
.header[data-v-724d1175] {
	background: linear-gradient(135deg, #667eea, #764ba2);
	-webkit-backdrop-filter: blur(0.625rem);
	        backdrop-filter: blur(0.625rem);
	padding: 0.625rem 0.9375rem;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 0.0625rem 0.5rem rgba(0, 0, 0, 0.1);
	position: -webkit-sticky;
	position: sticky;
	top: 0;
	z-index: 100;
	border-bottom: 0.03125rem solid rgba(255, 255, 255, 0.2);
	/* 确保导航栏不会被状态栏遮挡 - 多种兼容方案 */
	margin-top: calc(-1 * constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	margin-top: calc(-1 * env(safe-area-inset-top));
	/* iOS 11.2+ */
	margin-top: calc(-1 * var(--status-bar-height, 0px));
	/* 备用方案 */

	padding-top: calc(0.625rem + constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	padding-top: calc(0.625rem + env(safe-area-inset-top));
	/* iOS 11.2+ */
	padding-top: calc(0.625rem + var(--status-bar-height, 0px));
	/* 备用方案 */
}

/* 导航栏左侧 - 参考detail-hourly.vue */
.nav-left[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.3125rem;
	cursor: pointer;
}
.nav-left[data-v-724d1175]:active {
	opacity: 0.7;
}
.nav-icon[data-v-724d1175] {
	font-size: 1.125rem;
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.nav-text[data-v-724d1175] {
	font-size: 1rem;
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}

/* 导航栏标题 */
.nav-title[data-v-724d1175] {
	font-size: 1.03125rem;
	font-weight: 700;
	color: #f4ebf4;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.subtitle[data-v-724d1175] {
	font-size: 0.75rem;
	color: #666;
	margin-top: 0.15625rem;
	display: block;
}

/* 导航栏右侧 */
.nav-right[data-v-724d1175] {
	min-width: 3.125rem;
	text-align: right;
}
.action-btn[data-v-724d1175] {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 0.375rem 0.75rem;
	border-radius: 0.625rem;
	transition: all 0.3s ease;
	text-align: center;
}
.cancel-btn[data-v-724d1175] {
	background: rgb(233, 64, 64);
	border: 0.0625rem solid rgba(108, 117, 125, 0.3);
}
.cancel-btn[data-v-724d1175]:active {
	background: rgba(108, 117, 125, 0.2);
	transform: scale(0.95);
}
.save-btn[data-v-724d1175] {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	border: 0.0625rem solid transparent;
}
.save-btn[data-v-724d1175]:active {
	transform: scale(0.95);
	opacity: 0.9;
}
.cancel-btn .btn-text[data-v-724d1175] {
	font-size: 0.875rem;
	color: #ffffff;
	font-weight: 500;
}
.save-btn .btn-text[data-v-724d1175] {
	font-size: 0.875rem;
	color: white;
	font-weight: 500;
}
.date-text[data-v-724d1175] {
	font-size: 0.75rem;
	color: #999;
	margin-top: 0.15625rem;
	display: block;
}
.form-container[data-v-724d1175] {
	padding: 1.25rem 0.9375rem 0.625rem;
	max-width: 23.4375rem;
	margin: 0 auto;
	position: relative;
	z-index: 1;
}
.section[data-v-724d1175] {
	background: rgba(255, 255, 255, 0.95);
	-webkit-backdrop-filter: blur(0.3125rem);
	        backdrop-filter: blur(0.3125rem);
	border-radius: 0.75rem;
	padding: 1.09375rem 0.9375rem;
	margin-bottom: 0.78125rem;
	box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.12);
	border: 0.03125rem solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}
.section[data-v-724d1175]:hover {
	transform: translateY(-0.125rem);
	box-shadow: 0 0.375rem 1.25rem rgba(0, 0, 0, 0.15);
}
.section-title[data-v-724d1175] {
	display: flex;
	align-items: center;
	margin-bottom: 0.78125rem;
	position: relative;
}
.title-content[data-v-724d1175] {
	display: flex;
	align-items: center;
}
.section-icon[data-v-724d1175] {
	font-size: 1.125rem;
	margin-right: 0.46875rem;
	filter: drop-shadow(0 0.0625rem 0.125rem rgba(0, 0, 0, 0.1));
}
.title-text[data-v-724d1175] {
	font-size: 1rem;
	font-weight: 700;
	color: #333;
	letter-spacing: 0.03125rem;
}
.title-line[data-v-724d1175] {
	flex: 1;
	height: 0.09375rem;
	background: linear-gradient(90deg, #667eea, #764ba2);
	margin-left: 0.625rem;
	border-radius: 0.0625rem;
	opacity: 0.6;
}

/* ==================== 销售模式选择器 ==================== */
.sales-mode[data-v-724d1175] {
	margin-bottom: 0.78125rem;
}
.mode-selector[data-v-724d1175] {
	padding: 0.3125rem 0;
}
.mode-options[data-v-724d1175] {
	display: flex;
	gap: 0.625rem;
}
.mode-option[data-v-724d1175] {
	flex: 1;
	background: rgba(255, 255, 255, 0.8);
	border: 0.0625rem solid #e8eaff;
	border-radius: 0.5rem;
	padding: 0.75rem 0.625rem;
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}
.mode-option[data-v-724d1175]::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea, #764ba2);
	opacity: 0;
	transition: opacity 0.3s ease;
	z-index: 0;
}
.mode-option.active[data-v-724d1175]::before {
	opacity: 0.1;
}
.mode-option.active[data-v-724d1175] {
	border-color: #667eea;
	background: rgba(102, 126, 234, 0.05);
	transform: translateY(-0.0625rem);
	box-shadow: 0 0.25rem 0.75rem rgba(102, 126, 234, 0.15);
}
.mode-header[data-v-724d1175] {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 0.25rem;
	position: relative;
	z-index: 1;
}
.mode-label[data-v-724d1175] {
	font-size: 0.875rem;
	font-weight: 600;
	color: #333;
	transition: color 0.3s ease;
}
.mode-option.active .mode-label[data-v-724d1175] {
	color: #667eea;
}
.mode-indicator[data-v-724d1175] {
	width: 1rem;
	height: 1rem;
	background: #667eea;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: scaleIn-724d1175 0.3s ease;
}
.indicator-icon[data-v-724d1175] {
	color: white;
	font-size: 0.625rem;
	font-weight: bold;
}
.mode-option.active .mode-desc[data-v-724d1175] {
	color: #555;
}
@keyframes scaleIn-724d1175 {
0% {
		transform: scale(0);
		opacity: 0;
}
100% {
		transform: scale(1);
		opacity: 1;
}
}

/* 表单网格布局 */
.form-grid[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.78125rem;
}
.form-row[data-v-724d1175] {
	display: flex;
	gap: 0.625rem;
	align-items: flex-start;
}
.form-item[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.46875rem;
	flex: 1;
}
.form-item.half-width[data-v-724d1175] {
	flex: 1;
	min-width: 0;
	/* 防止内容溢出 */
}

/* ==================== 增强表单设计 ==================== */

/* 增强表单容器 */
.enhanced-form-container[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.9375rem;
	padding: 0.3125rem 0;
}

/* 只读信息区域 */
.readonly-info-section[data-v-724d1175] {
	display: flex;
	gap: 0.625rem;
	margin-bottom: 0.3125rem;
}
.info-card[data-v-724d1175] {
	flex: 1;
	background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
	border-radius: 0.5rem;
	padding: 0.75rem 0.625rem;
	border: 0.03125rem solid #e8eaff;
	box-shadow: 0 0.0625rem 0.25rem rgba(99, 102, 241, 0.08);
	transition: all 0.3s ease;
}
.info-card[data-v-724d1175]:hover {
	transform: translateY(-0.0625rem);
	box-shadow: 0 0.125rem 0.375rem rgba(99, 102, 241, 0.12);
}
.info-item[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}
.info-icon[data-v-724d1175] {
	font-size: 1rem;
	width: 1.5rem;
	height: 1.5rem;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
	border-radius: 0.375rem;
	box-shadow: 0 0.0625rem 0.1875rem rgba(99, 102, 241, 0.2);
}
.info-content[data-v-724d1175] {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 0.125rem;
}
.info-label[data-v-724d1175] {
	font-size: 0.75rem;
	color: #6b7280;
	font-weight: 500;
}
.info-value[data-v-724d1175] {
	font-size: 0.875rem;
	color: #1f2937;
	font-weight: 600;
}

/* 分隔线 */
.form-divider[data-v-724d1175] {
	height: 0.03125rem;
	background: linear-gradient(90deg, transparent 0%, #e5e7eb 50%, transparent 100%);
	margin: 0.625rem 0;
}

/* 可编辑信息区域 */
.editable-info-section[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

/* 主要字段容器 */
.primary-field-container[data-v-724d1175] {
	margin-bottom: 0.25rem;
}

/* 次要字段容器 */
.secondary-fields-container[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.625rem;
}

/* 字段组通用样式 */
.field-group[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.375rem;
}

/* 字段头部 */
.field-header[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.field-icon[data-v-724d1175] {
	font-size: 0.875rem;
	width: 1.25rem;
	height: 1.25rem;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 0.3125rem;
	transition: all 0.3s ease;
}
.field-icon.primary[data-v-724d1175] {
	background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
	box-shadow: 0 0.0625rem 0.1875rem rgba(251, 191, 36, 0.3);
}
.field-icon.secondary[data-v-724d1175] {
	background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
	box-shadow: 0 0.0625rem 0.1875rem rgba(156, 163, 175, 0.2);
}
.field-label[data-v-724d1175] {
	font-weight: 600;
	transition: color 0.3s ease;
}
.field-label.primary[data-v-724d1175] {
	font-size: 0.9375rem;
	color: #92400e;
}
.field-label.secondary[data-v-724d1175] {
	font-size: 0.875rem;
	color: #374151;
}
.field-label.required[data-v-724d1175]::after {
	content: '*';
	color: #ef4444;
	margin-left: 0.1875rem;
	font-weight: bold;
	font-size: 1rem;
}
.field-unit[data-v-724d1175] {
	font-size: 0.75rem;
	color: #6b7280;
	font-weight: 500;
	margin-left: auto;
}

/* 增强输入框包装器 */
.enhanced-input-wrapper[data-v-724d1175] {
	position: relative;
	border-radius: 0.4375rem;
	transition: all 0.3s ease;
	overflow: hidden;
}
.enhanced-input-wrapper.primary[data-v-724d1175] {
	background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
	border: 0.0625rem solid #f59e0b;
	box-shadow: 0 0.125rem 0.375rem rgba(245, 158, 11, 0.15);
}
.enhanced-input-wrapper.secondary[data-v-724d1175] {
	background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
	border: 0.0625rem solid #d1d5db;
	box-shadow: 0 0.0625rem 0.25rem rgba(156, 163, 175, 0.1);
}
.enhanced-input-wrapper[data-v-724d1175]:focus-within {
	transform: translateY(-0.0625rem);
}
.enhanced-input-wrapper.primary[data-v-724d1175]:focus-within {
	border-color: #d97706;
	box-shadow: 0 0.1875rem 0.5rem rgba(245, 158, 11, 0.25);
}
.enhanced-input-wrapper.secondary[data-v-724d1175]:focus-within {
	border-color: #9ca3af;
	box-shadow: 0 0.125rem 0.375rem rgba(156, 163, 175, 0.15);
}

/* 增强输入框 */
.enhanced-input[data-v-724d1175] {
	width: 100%;
	height: 2.75rem;
	padding: 0 0.75rem;
	border: none;
	background: transparent;
	font-size: 0.875rem;
	font-weight: 500;
	transition: all 0.3s ease;
	outline: none;
}
.enhanced-input.primary[data-v-724d1175] {
	color: #92400e;
	font-weight: 600;
}
.enhanced-input.secondary[data-v-724d1175] {
	color: #374151;
}
.enhanced-input[data-v-724d1175]::-webkit-input-placeholder {
	color: #9ca3af;
	font-weight: 400;
}
.enhanced-input[data-v-724d1175]::placeholder {
	color: #9ca3af;
	font-weight: 400;
}
.enhanced-input.primary[data-v-724d1175]::-webkit-input-placeholder {
	color: #d97706;
	opacity: 0.7;
}
.enhanced-input.primary[data-v-724d1175]::placeholder {
	color: #d97706;
	opacity: 0.7;
}

/* 主要字段特殊样式 */
.primary-field[data-v-724d1175] {
	background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
	border-radius: 0.5rem;
	padding: 0.75rem;
	border: 0.03125rem solid #fbbf24;
	box-shadow: 0 0.125rem 0.375rem rgba(251, 191, 36, 0.1);
	position: relative;
	overflow: hidden;
}
.primary-field[data-v-724d1175]::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 0.09375rem;
	background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
}

/* 次要字段样式 */
.secondary-field[data-v-724d1175] {
	background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
	border-radius: 0.4375rem;
	padding: 0.625rem;
	border: 0.03125rem solid #e5e7eb;
	box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.04);
	transition: all 0.3s ease;
}
.secondary-field[data-v-724d1175]:hover {
	transform: translateY(-0.03125rem);
	box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.08);
}

/* 可编辑字段样式 */
.form-item .label.required[data-v-724d1175]::after {
	content: '*';
	color: #ff4757;
	margin-left: 0.15625rem;
	font-weight: bold;
}
.editable-wrapper[data-v-724d1175] {
	position: relative;
}
.editable-input[data-v-724d1175] {
	width: 100%;
	height: 2.5rem;
	padding: 0 0.625rem;
	border: 0.0625rem solid #e1e8ed;
	border-radius: 0.375rem;
	font-size: 0.875rem;
	background: white;
	transition: all 0.3s ease;
}

/* 紧凑型输入框 - 优化宽度 */
.compact-input[data-v-724d1175] {
	max-width: 8.75rem;
	min-width: 6.25rem;
}
.editable-input[data-v-724d1175]:focus {
	border-color: #667eea;
	box-shadow: 0 0 0 0.1875rem rgba(102, 126, 234, 0.1);
	outline: none;
}

/* 只读字段样式 */
.readonly-wrapper[data-v-724d1175] {
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border: 0.0625rem solid #dee2e6;
	border-radius: 0.375rem;
	padding: 0.625rem;
}
.readonly-text[data-v-724d1175] {
	font-size: 0.875rem;
	color: #495057;
	font-weight: 500;
}

/* 收益汇总卡片样式 */
.summary-cards[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.46875rem;
}
.summary-card[data-v-724d1175] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.78125rem;
	border-radius: 0.5rem;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}
.summary-card[data-v-724d1175]::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: 0.1;
	transition: opacity 0.3s ease;
}
.income-card[data-v-724d1175] {
	background: linear-gradient(135deg, #52c41a, #73d13d);
}
.cost-card[data-v-724d1175] {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
}
.tea-picking-cost-card[data-v-724d1175] {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: white;
}
.labor-cost-card[data-v-724d1175] {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
	color: white;
}
.profit-card.positive-profit[data-v-724d1175] {
	background: linear-gradient(135deg, #f56a00, #fa8c16);
	color: white;
}
.profit-card.negative-profit[data-v-724d1175] {
	background: linear-gradient(135deg, #ff4d4f, #ff7875);
}
.card-header[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.card-icon[data-v-724d1175] {
	font-size: 1rem;
}
.card-title[data-v-724d1175] {
	font-size: 0.875rem;
	font-weight: 600;
}
.card-value[data-v-724d1175] {
	text-align: right;
}
.value-amount[data-v-724d1175] {
	font-size: 1rem;
	font-weight: 700;
	display: block;
}
.tea-picking-cost-amount[data-v-724d1175] {
	color: white;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
.labor-cost-amount[data-v-724d1175] {
	color: white;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
.profit-rate[data-v-724d1175] {
	font-size: 0.75rem;
	opacity: 0.9;
	margin-top: 0.15625rem;
	display: block;
}
.form-item.readonly[data-v-724d1175] {
	background: #f8f9fa;
	border-radius: 0.375rem;
	padding: 0.46875rem;
}

/* 支出明细样式 */
.cost-grid[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.78125rem;
}
.cost-item[data-v-724d1175] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.78125rem;
	border-radius: 0.5rem;
	transition: all 0.3s ease;
}
.cost-item.readonly[data-v-724d1175] {
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border: 0.0625rem solid #dee2e6;
}
.cost-item.editable[data-v-724d1175] {
	background: linear-gradient(135deg, #fff5f5, #ffe8e8);
	border: 0.0625rem solid #fbb6ce;
}
.cost-label[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.label-icon[data-v-724d1175] {
	font-size: 0.875rem;
}
.cost-value[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.3125rem;
}
.readonly-value .value-text[data-v-724d1175] {
	font-size: 0.875rem;
	font-weight: 600;
	color: #495057;
}
.editable-value[data-v-724d1175] {
	position: relative;
}
.value-input[data-v-724d1175] {
	width: 6.25rem;
	height: 1.875rem;
	padding: 0 0.46875rem;
	border: 0.0625rem solid #e1e8ed;
	border-radius: 0.25rem;
	font-size: 0.8125rem;
	text-align: right;
	background: white;
}
.value-input[data-v-724d1175]:focus {
	border-color: #667eea;
	outline: none;
}
.currency[data-v-724d1175] {
	font-size: 0.75rem;
	color: #6c757d;
	font-weight: 500;
}
.label[data-v-724d1175] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
}
.input-wrapper[data-v-724d1175] {
	flex: 1;
	position: relative;
}
.input[data-v-724d1175] {
	width: 100%;
	height: 2.1875rem;
	padding: 0 0.625rem;
	border: 0.0625rem solid #e0e0e0;
	border-radius: 0.375rem;
	font-size: 0.875rem;
	background: white;
}
.input[data-v-724d1175]:focus {
	border-color: #2e7d32;
	outline: none;
}
.input-text[data-v-724d1175] {
	display: block;
	height: 2.1875rem;
	line-height: 2.1875rem;
	padding: 0 0.625rem;
	border: 0.0625rem solid #e0e0e0;
	border-radius: 0.375rem;
	font-size: 0.875rem;
	background: white;
	color: #333;
}
.input-text.calculated[data-v-724d1175] {
	background: #f8f9fa;
	border-color: #f0f0f0;
	font-weight: 600;
}
.positive[data-v-724d1175] {
	color: #2e7d32;
}
.negative[data-v-724d1175] {
	color: #d32f2f;
}



/* 响应式设计 */
@media (max-width: 750rpx) {
.header[data-v-724d1175] {
		padding: 0.625rem;
		/* 移动端状态栏安全区域适配 - 多种兼容方案 */
		padding-top: calc(0.625rem + constant(safe-area-inset-top));
		/* iOS 11.0-11.2 */
		padding-top: calc(0.625rem + env(safe-area-inset-top));
		/* iOS 11.2+ */
		padding-top: calc(0.625rem + var(--status-bar-height, 0px));
		/* 备用方案 */
}

	/* 小屏幕导航栏适配 - 参考detail-hourly.vue */
.nav-icon[data-v-724d1175] {
		font-size: 1rem;
}
.nav-text[data-v-724d1175] {
		font-size: 0.875rem;
}
.nav-title[data-v-724d1175] {
		font-size: 1.125rem;
}
.action-btn[data-v-724d1175] {
		padding: 0.3125rem 0.625rem;
		font-size: 0.8125rem;
}
.btn-text[data-v-724d1175] {
		font-size: 0.75rem !important;
}
.form-container[data-v-724d1175] {
		padding: 0.9375rem 0.625rem 0.625rem;
}
.section[data-v-724d1175] {
		padding: 0.9375rem 0.78125rem;
		margin-bottom: 0.625rem;
}
.form-grid[data-v-724d1175] {
		gap: 0.625rem;
}
.form-row[data-v-724d1175] {
		flex-direction: column;
		gap: 0.46875rem;
}
.form-item.half-width[data-v-724d1175] {
		max-width: none;
}
.compact-input[data-v-724d1175] {
		max-width: none;
		min-width: auto;
}
.cost-item[data-v-724d1175] {
		flex-direction: column;
		align-items: stretch;
		gap: 0.375rem;
}
.cost-value[data-v-724d1175] {
		justify-content: flex-end;
}
.value-input[data-v-724d1175] {
		width: 4.6875rem;
}
.summary-cards[data-v-724d1175] {
		gap: 0.375rem;
}
.summary-card[data-v-724d1175] {
		padding: 0.625rem;
}
.card-header[data-v-724d1175] {
		gap: 0.25rem;
}
.card-icon[data-v-724d1175] {
		font-size: 0.8125rem;
}
.card-title[data-v-724d1175] {
		font-size: 0.75rem;
}
.value-amount[data-v-724d1175] {
		font-size: 0.8125rem;
}
.profit-rate[data-v-724d1175] {
		font-size: 0.625rem;
}

	/* ==================== 增强表单响应式样式 ==================== */

	/* 只读信息区域响应式 */
.readonly-info-section[data-v-724d1175] {
		flex-direction: column;
		gap: 0.5rem;
}
.info-card[data-v-724d1175] {
		padding: 0.625rem 0.5rem;
		border-radius: 0.375rem;
}
.info-icon[data-v-724d1175] {
		font-size: 0.875rem;
		width: 1.25rem;
		height: 1.25rem;
		border-radius: 0.3125rem;
}
.info-label[data-v-724d1175] {
		font-size: 0.6875rem;
}
.info-value[data-v-724d1175] {
		font-size: 0.8125rem;
}

	/* 可编辑区域响应式 */
.editable-info-section[data-v-724d1175] {
		gap: 0.625rem;
}
.primary-field[data-v-724d1175] {
		padding: 0.625rem;
		border-radius: 0.4375rem;
}
.secondary-field[data-v-724d1175] {
		padding: 0.5rem;
		border-radius: 0.375rem;
}
.field-icon[data-v-724d1175] {
		font-size: 0.75rem;
		width: 1.125rem;
		height: 1.125rem;
		border-radius: 0.25rem;
}
.field-label.primary[data-v-724d1175] {
		font-size: 0.875rem;
}
.field-label.secondary[data-v-724d1175] {
		font-size: 0.8125rem;
}
.field-unit[data-v-724d1175] {
		font-size: 0.6875rem;
}
.enhanced-input[data-v-724d1175] {
		height: 2.5rem;
		padding: 0 0.625rem;
		font-size: 0.8125rem;
}
.enhanced-input-wrapper[data-v-724d1175] {
		border-radius: 0.375rem;
}
}

/* 大屏幕优化 */
@media (min-width: 750rpx) {
.readonly-info-section[data-v-724d1175] {
		gap: 0.75rem;
}
.info-card[data-v-724d1175] {
		padding: 0.875rem 0.75rem;
}
.secondary-fields-container[data-v-724d1175] {
		display: flex;
		flex-direction: row;
		gap: 0.75rem;
}
.secondary-field[data-v-724d1175] {
		flex: 1;
}
.enhanced-input-wrapper[data-v-724d1175]:hover {
		transform: translateY(-0.03125rem);
}
.enhanced-input-wrapper.primary[data-v-724d1175]:hover {
		box-shadow: 0 0.25rem 0.625rem rgba(245, 158, 11, 0.2);
}
.enhanced-input-wrapper.secondary[data-v-724d1175]:hover {
		box-shadow: 0 0.1875rem 0.5rem rgba(156, 163, 175, 0.12);
}
}

/* ==================== 工作记录信息样式 ==================== */
.work-record-info[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

/* ==================== 销售记录列表样式 ==================== */
.sales-records[data-v-724d1175] {
	margin-top: 1rem;
}
.sales-count[data-v-724d1175] {
	font-size: 0.6875rem;
	color: #666;
	margin-left: 0.25rem;
}
.production-summary[data-v-724d1175] {
	background: #f8f9fa;
	padding: 0.625rem;
	border-radius: 0.375rem;
	margin-bottom: 0.75rem;
}
.time-period-breakdown[data-v-724d1175] {
	margin-bottom: 0.5rem;
	padding-bottom: 0.5rem;
	border-bottom: 0.03125rem solid #e0e0e0;
}
.breakdown-title[data-v-724d1175] {
	margin-bottom: 0.375rem;
}
.breakdown-label[data-v-724d1175] {
	font-size: 0.75rem;
	color: #666;
	font-weight: 500;
}
.breakdown-items[data-v-724d1175] {
	display: flex;
	gap: 0.625rem;
	flex-wrap: wrap;
}
.breakdown-item[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.1875rem;
	padding: 0.1875rem 0.375rem;
	background: white;
	border-radius: 0.25rem;
	border: 0.03125rem solid #e0e0e0;
}
.period-label[data-v-724d1175] {
	font-size: 0.6875rem;
	color: #666;
}
.period-value[data-v-724d1175] {
	font-size: 0.6875rem;
	font-weight: 600;
	color: #3498db;
}
.allocation-status[data-v-724d1175] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.summary-item[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.25rem;
}
.summary-label[data-v-724d1175] {
	font-size: 0.75rem;
	color: #666;
}
.summary-value[data-v-724d1175] {
	font-size: 0.8125rem;
	font-weight: 600;
	color: #2c3e50;
}
.summary-value.warning[data-v-724d1175] {
	color: #e74c3c;
}
.summary-value.error[data-v-724d1175] {
	color: #e74c3c;
	font-weight: 700;
}
.summary-value.success[data-v-724d1175] {
	color: #27ae60;
}
.production-summary.error[data-v-724d1175] {
	border: 0.0625rem solid #e74c3c;
	background: #ffeaea;
}
.validation-message[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.25rem;
	margin-top: 0.375rem;
	padding: 0.25rem 0.375rem;
	background: #fee;
	border-radius: 0.25rem;
	border-left: 0.125rem solid #e74c3c;
}
.error-icon[data-v-724d1175] {
	font-size: 0.625rem;
}
.error-text[data-v-724d1175] {
	font-size: 0.6875rem;
	color: #e74c3c;
	font-weight: 500;
}
.field-input.error[data-v-724d1175] {
	border-color: #e74c3c;
	background: #ffeaea;
}
.error-tip[data-v-724d1175] {
	position: absolute;
	top: 100%;
	left: 0;
	font-size: 0.625rem;
	color: #e74c3c;
	margin-top: 0.125rem;
	padding: 0.125rem 0.25rem;
	background: #fee;
	border-radius: 0.125rem;
	white-space: nowrap;
}
.sales-record-list[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.75rem;
}

/* 日期工作汇总样式 */
.daily-summary[data-v-724d1175] {
	margin-bottom: 1rem;
}
.daily-summary-card[data-v-724d1175] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 0.625rem;
	padding: 1rem;
	color: white;
	box-shadow: 0 0.25rem 0.75rem rgba(102, 126, 234, 0.3);
}
.summary-header[data-v-724d1175] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.75rem;
}
.date-display[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.date-icon[data-v-724d1175] {
	font-size: 1rem;
}
.date-text[data-v-724d1175] {
	font-size: 1rem;
	font-weight: 600;
}
.worker-count[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.25rem;
	background: rgba(255, 255, 255, 0.2);
	padding: 0.25rem 0.5rem;
	border-radius: 0.625rem;
}
.count-icon[data-v-724d1175] {
	font-size: 0.75rem;
}
.count-text[data-v-724d1175] {
	font-size: 0.75rem;
	font-weight: 500;
}
.workers-list[data-v-724d1175] {
	margin-bottom: 0.75rem;
}
.workers-header[data-v-724d1175] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.375rem;
}
.expand-btn[data-v-724d1175] {
	background: rgba(255, 255, 255, 0.3);
	padding: 0.25rem 0.5rem;
	border-radius: 0.375rem;
	cursor: pointer;
	transition: all 0.3s ease;
	/* 确保在移动端可点击 */
	min-width: 2.5rem;
	min-height: 1rem;
	display: flex;
	align-items: center;
	justify-content: center;
	/* 防止文本选择 */
	user-select: none;
	-webkit-user-select: none;
	/* 触摸反馈 */
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0.2);
}
.expand-btn[data-v-724d1175]:active {
	background: rgba(255, 255, 255, 0.5);
	transform: scale(0.95);
}
.expand-text[data-v-724d1175] {
	font-size: 0.625rem;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 500;
}
.workers-title[data-v-724d1175] {
	font-size: 0.75rem;
	opacity: 0.9;
	font-weight: 500;
}
.workers-tags[data-v-724d1175] {
	display: flex;
	flex-wrap: wrap;
	gap: 0.375rem;
}
.worker-tag[data-v-724d1175] {
	background: rgba(255, 255, 255, 0.2);
	padding: 0.25rem 0.5rem;
	border-radius: 0.5rem;
	display: flex;
	align-items: center;
	gap: 0.25rem;
}
.worker-name[data-v-724d1175] {
	font-size: 0.75rem;
	font-weight: 500;
}
.worker-production[data-v-724d1175] {
	font-size: 0.6875rem;
	opacity: 0.8;
}
.summary-stats[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}
.stats-row[data-v-724d1175] {
	display: flex;
	justify-content: space-between;
	gap: 0.5rem;
}
.stat-item[data-v-724d1175] {
	flex: 1;
	background: rgba(255, 255, 255, 0.15);
	padding: 0.625rem;
	border-radius: 0.5rem;
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.stat-icon[data-v-724d1175] {
	font-size: 0.875rem;
}
.stat-content[data-v-724d1175] {
	flex: 1;
}
.stat-label[data-v-724d1175] {
	font-size: 0.6875rem;
	opacity: 0.8;
	display: block;
	margin-bottom: 0.125rem;
}
.stat-value[data-v-724d1175] {
	font-size: 0.8125rem;
	font-weight: 600;
	display: block;
}
.stat-value.primary[data-v-724d1175] {
	color: #fff;
	font-size: 0.875rem;
}
.stat-value.secondary[data-v-724d1175] {
	color: #ffd700;
}

/* 销售记录管理样式优化 */
.sales-summary[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
	margin-left: 0.375rem;
}
.sales-count[data-v-724d1175] {
	background: #e3f2fd;
	color: #1976d2;
	padding: 0.1875rem 0.4375rem;
	border-radius: 0.375rem;
	font-size: 0.8125rem;
	font-weight: 500;
}
.sales-allocation[data-v-724d1175] {
	background: #f3e5f5;
	color: #7b1fa2;
	padding: 0.1875rem 0.4375rem;
	border-radius: 0.375rem;
	font-size: 0.8125rem;
	font-weight: 500;
}

/* 紧凑型销售记录列表 */
.compact-sales-list[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}
.compact-sales-item[data-v-724d1175] {
	background: white;
	border-radius: 0.375rem;
	padding: 0.5rem;
	box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.08);
	border: 0.0625rem solid transparent;
	transition: all 0.3s ease;
}
.compact-sales-item.error[data-v-724d1175] {
	border-color: #ff5722;
	background: #fff3f3;
}
.item-header[data-v-724d1175] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.375rem;
}
.customer-badge[data-v-724d1175] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	width: 1.5rem;
	height: 1.5rem;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.75rem;
	font-weight: 600;
}
.customer-number[data-v-724d1175] {
	color: white;
	font-size: 0.8125rem;
}
.item-actions[data-v-724d1175] {
	display: flex;
	align-items: center;
}
.delete-btn[data-v-724d1175] {
	background: #ffebee;
	color: #f44336;
	width: 1.375rem;
	height: 1.375rem;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1rem;
	font-weight: bold;
	transition: all 0.3s ease;
}
.delete-btn[data-v-724d1175]:active {
	background: #ffcdd2;
	transform: scale(0.9);
}
.delete-icon[data-v-724d1175] {
	color: #f44336;
}

/* 紧凑型输入区域 */
.compact-inputs[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.375rem;
}
.input-group[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.input-label[data-v-724d1175] {
	font-size: 0.875rem;
	color: #666;
	min-width: 2.1875rem;
	font-weight: 500;
}
.input-container[data-v-724d1175] {
	flex: 1;
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 0.25rem;
	padding: 0 0.375rem;
	border: 0.0625rem solid transparent;
	transition: all 0.3s ease;
}
.input-container[data-v-724d1175]:focus-within {
	background: white;
	border-color: #667eea;
	box-shadow: 0 0 0 0.125rem rgba(102, 126, 234, 0.1);
}
.compact-input[data-v-724d1175] {
	flex: 1;
	border: none;
	background: transparent;
	padding: 0.375rem 0;
	font-size: 0.9375rem;
	color: #333;
}
.compact-input.error[data-v-724d1175] {
	color: #f44336;
}
.compact-input[data-v-724d1175]::-webkit-input-placeholder {
	color: #bbb;
	font-size: 0.875rem;
}
.compact-input[data-v-724d1175]::placeholder {
	color: #bbb;
	font-size: 0.875rem;
}
.input-unit[data-v-724d1175] {
	font-size: 0.8125rem;
	color: #999;
	margin-left: 0.25rem;
	min-width: 1.40625rem;
}
.name-input[data-v-724d1175] {
	font-size: 0.875rem;
}

/* 输入组特定样式 */
.production-group .input-container[data-v-724d1175] {
	background: #e8f5e8;
}
.production-group .input-container[data-v-724d1175]:focus-within {
	border-color: #4caf50;
	box-shadow: 0 0 0 0.125rem rgba(76, 175, 80, 0.1);
}
.price-group .input-container[data-v-724d1175] {
	background: #fff3e0;
}
.price-group .input-container[data-v-724d1175]:focus-within {
	border-color: #ff9800;
	box-shadow: 0 0 0 0.125rem rgba(255, 152, 0, 0.1);
}
.name-group .input-container[data-v-724d1175] {
	background: #f3e5f5;
}
.name-group .input-container[data-v-724d1175]:focus-within {
	border-color: #9c27b0;
	box-shadow: 0 0 0 0.125rem rgba(156, 39, 176, 0.1);
}

/* 项目底部 */
.item-footer[data-v-724d1175] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 0.375rem;
	padding-top: 0.375rem;
	border-top: 0.03125rem solid #f0f0f0;
}
.subtotal-info[data-v-724d1175] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.subtotal-amount[data-v-724d1175] {
	font-size: 1rem;
	font-weight: 600;
	color: #4caf50;
}
.error-text[data-v-724d1175] {
	font-size: 0.75rem;
	color: #f44336;
	background: #ffebee;
	padding: 0.125rem 0.3125rem;
	border-radius: 0.25rem;
}

/* 添加按钮区域 */
.add-section[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.375rem;
	margin-top: 0.5rem;
}
.add-btn[data-v-724d1175] {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 0.5rem 0.75rem;
	border-radius: 0.375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 0.25rem;
	font-size: 0.8125rem;
	font-weight: 500;
	transition: all 0.3s ease;
	box-shadow: 0 0.125rem 0.375rem rgba(102, 126, 234, 0.3);
}
.add-btn[data-v-724d1175]:active {
	transform: translateY(0.0625rem);
	box-shadow: 0 0.0625rem 0.25rem rgba(102, 126, 234, 0.3);
}

/* 移除禁用状态，始终允许添加客户 */
.add-icon[data-v-724d1175] {
	font-size: 1rem;
	font-weight: bold;
}
.add-text[data-v-724d1175] {
	font-size: 0.9375rem;
}
.allocation-status[data-v-724d1175] {
	text-align: center;
}
.status-text[data-v-724d1175] {
	font-size: 0.875rem;
	color: #666;
	padding: 0.3125rem 0.5625rem;
	background: #f8f9fa;
	border-radius: 0.5rem;
	display: inline-block;
}
.status-text.warning[data-v-724d1175] {
	color: #f44336;
	background: #ffebee;
}
.action-btn[data-v-724d1175] {
	padding: 0.25rem 0.375rem;
	border-radius: 0.25rem;
	cursor: pointer;
}
.delete-btn[data-v-724d1175] {
	background: #fee;
	color: #e74c3c;
}
.btn-icon[data-v-724d1175] {
	font-size: 0.625rem;
}
.card-content[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.625rem;
}
.field-group[data-v-724d1175] {
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
}
.field-header[data-v-724d1175] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.field-label[data-v-724d1175] {
	font-size: 0.75rem;
	color: #555;
	font-weight: 500;
}
.field-actions[data-v-724d1175] {
	display: flex;
	gap: 0.375rem;
}
.auto-fill-btn[data-v-724d1175] {
	font-size: 0.6875rem;
	color: #3498db;
	padding: 0.125rem 0.25rem;
	border-radius: 0.125rem;
	background: #e3f2fd;
	cursor: pointer;
}
.input-wrapper[data-v-724d1175] {
	position: relative;
}
.field-input[data-v-724d1175] {
	width: 100%;
	padding: 0.5rem 0.625rem;
	border: 0.0625rem solid #e0e0e0;
	border-radius: 0.375rem;
	font-size: 0.8125rem;
	background: #fafafa;
	transition: all 0.3s ease;
}
.field-input[data-v-724d1175]:focus {
	border-color: #3498db;
	background: white;
	box-shadow: 0 0 0 0.125rem rgba(52, 152, 219, 0.1);
}
.subtotal[data-v-724d1175] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.375rem 0.5rem;
	background: #f0f8ff;
	border-radius: 0.25rem;
	margin-top: 0.25rem;
}
.subtotal-label[data-v-724d1175] {
	font-size: 0.75rem;
	color: #666;
}
.subtotal-value[data-v-724d1175] {
	font-size: 0.8125rem;
	font-weight: 600;
	color: #3498db;
}
.add-sales-section[data-v-724d1175] {
	margin-top: 0.75rem;
	text-align: center;
}
.add-sales-btn[data-v-724d1175] {
	display: inline-flex;
	align-items: center;
	gap: 0.25rem;
	padding: 0.5rem 1rem;
	background: linear-gradient(135deg, #3498db, #2980b9);
	color: white;
	border-radius: 0.375rem;
	font-size: 0.8125rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}
.add-sales-btn[data-v-724d1175]:active {
	transform: scale(0.98);
}
.add-sales-btn.disabled[data-v-724d1175] {
	background: #bdc3c7;
	cursor: not-allowed;
}
.add-icon[data-v-724d1175] {
	font-size: 0.75rem;
}
.add-text[data-v-724d1175] {
	font-size: 0.75rem;
}
.add-tip[data-v-724d1175] {
	display: block;
	margin-top: 0.375rem;
	font-size: 0.6875rem;
	color: #999;
}
