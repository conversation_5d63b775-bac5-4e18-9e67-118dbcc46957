
.monthly-analysis[data-v-be17a23d] {
	padding: 0.625rem;
}

/* 月份选择器 */
.month-selector[data-v-be17a23d] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.selector-header[data-v-be17a23d] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.selector-title[data-v-be17a23d] {
	font-size: 1.125rem;
	font-weight: 600;
	color: #333;
}
.month-picker[data-v-be17a23d] {
	display: flex;
	align-items: center;
	padding: 0.3125rem 0.625rem;
	background-color: #f5f5f5;
	border-radius: 0.25rem;
}
.month-text[data-v-be17a23d] {
	font-size: 1rem;
	color: #2e7d32;
	margin-right: 0.3125rem;
}
.picker-arrow[data-v-be17a23d] {
	font-size: 0.75rem;
}

/* 概览卡片 */
.overview-section[data-v-be17a23d] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.overview-title[data-v-be17a23d] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.overview-cards[data-v-be17a23d] {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.625rem;
}
.overview-card[data-v-be17a23d] {
	text-align: center;
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
}
.card-value[data-v-be17a23d] {
	display: block;
	font-size: 1.125rem;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 0.25rem;
}
.card-label[data-v-be17a23d] {
	font-size: 0.875rem;
	color: #666;
}

/* 分析部分 */
.analysis-section[data-v-be17a23d] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.section-title[data-v-be17a23d] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}



/* 统计网格 */
.stats-container[data-v-be17a23d] {
	margin-bottom: 0.9375rem;
}
.stats-title[data-v-be17a23d] {
	font-size: 0.875rem;
	color: #666;
	margin-bottom: 0.46875rem;
}
.stats-grid[data-v-be17a23d] {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 0.46875rem;
}
.stat-item[data-v-be17a23d] {
	text-align: center;
	padding: 0.46875rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
}
.stat-value[data-v-be17a23d] {
	display: block;
	font-size: 1.125rem;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 0.15625rem;
}
.stat-label[data-v-be17a23d] {
	font-size: 0.875rem;
	color: #666;
}

/* 对比分析 */
.comparison-section[data-v-be17a23d] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.comparison-cards[data-v-be17a23d] {
	display: flex;
	flex-direction: column;
	gap: 0.46875rem;
}
.comparison-card[data-v-be17a23d] {
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
}
.comparison-title[data-v-be17a23d] {
	font-size: 0.875rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.comparison-item[data-v-be17a23d] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.375rem;
	padding-top: 0.0625rem;
}
.comparison-item[data-v-be17a23d]:first-of-type {
	margin-top: 0.25rem;
}
.comparison-label[data-v-be17a23d] {
	font-size: 0.875rem;
	color: #666;
}
.comparison-value[data-v-be17a23d] {
	font-size: 0.875rem;
	font-weight: 600;
}
.comparison-value.positive[data-v-be17a23d] {
	color: #2e7d32;
}
.comparison-value.negative[data-v-be17a23d] {
	color: #f44336;
}


.yearly-analysis[data-v-325d9688] {
	padding: 0.625rem;
}

/* 年份选择器 */
.year-selector[data-v-325d9688] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.selector-header[data-v-325d9688] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.selector-title[data-v-325d9688] {
	font-size: 1.125rem;
	font-weight: 600;
	color: #333;
}
.year-picker[data-v-325d9688] {
	display: flex;
	align-items: center;
	padding: 0.3125rem 0.625rem;
	background-color: #f5f5f5;
	border-radius: 0.25rem;
}
.year-text[data-v-325d9688] {
	font-size: 1rem;
	color: #2e7d32;
	margin-right: 0.3125rem;
}
.picker-arrow[data-v-325d9688] {
	font-size: 0.75rem;
}

/* 概览卡片 */
.overview-section[data-v-325d9688] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.overview-title[data-v-325d9688] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.overview-cards[data-v-325d9688] {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.625rem;
}
.overview-card[data-v-325d9688] {
	text-align: center;
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
}
.card-value[data-v-325d9688] {
	display: block;
	font-size: 1.125rem;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 0.25rem;
}
.card-label[data-v-325d9688] {
	font-size: 0.8125rem;
	color: #666;
}
.section-title[data-v-325d9688] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}





/* 效率分析 */
.efficiency-section[data-v-325d9688] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.efficiency-cards[data-v-325d9688] {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.625rem;
}
.efficiency-card[data-v-325d9688] {
	text-align: center;
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
}
.efficiency-title[data-v-325d9688] {
	display: block;
	font-size: 0.8125rem;
	color: #666;
	margin-bottom: 0.25rem;
}
.efficiency-value[data-v-325d9688] {
	display: block;
	font-size: 1rem;
	font-weight: 600;
	color: #2e7d32;
}

/* 对比分析样式 */
.comparison-section[data-v-325d9688] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.comparison-cards[data-v-325d9688] {
	display: flex;
	flex-direction: column;
	gap: 0.46875rem;
}
.comparison-card[data-v-325d9688] {
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
}
.comparison-title[data-v-325d9688] {
	font-size: 0.875rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.comparison-item[data-v-325d9688] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.375rem;
	padding-top: 0.0625rem;
}
.comparison-item[data-v-325d9688]:first-of-type {
	margin-top: 0.25rem;
}
.comparison-label[data-v-325d9688] {
	font-size: 0.875rem;
	color: #666;
}
.comparison-value[data-v-325d9688] {
	font-size: 0.875rem;
	font-weight: 600;
}
.comparison-value.positive[data-v-325d9688] {
	color: #2e7d32;
}
.comparison-value.negative[data-v-325d9688] {
	color: #f44336;
}


.worker-analysis[data-v-6e77590c] {
	padding: 0.625rem;
}

/* 统一选择器面板 */
.unified-selector[data-v-6e77590c] {
	background-color: white;
	border-radius: 0.5rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.08);
}

/* 标题区域 */
.selector-title-section[data-v-6e77590c] {
	margin-bottom: 0.9375rem;
	padding-bottom: 0.625rem;
	border-bottom: 0.03125rem solid #f0f0f0;
}
.main-title[data-v-6e77590c] {
	font-size: 1.125rem;
	font-weight: 600;
	color: #333;
}

/* 选择器内容区域 */
.selector-content[data-v-6e77590c] {
	display: flex;
	flex-direction: column;
	gap: 0.9375rem;
}

/* 选择区域通用样式 */
.worker-selection-area[data-v-6e77590c],
.date-selection-area[data-v-6e77590c] {
	display: flex;
	flex-direction: column;
	gap: 0.46875rem;
}
.name-input-wrapper[data-v-6e77590c] {
	display: flex;
	align-items: center;
	gap: 0.46875rem;
	position: relative;
}
.form-input[data-v-6e77590c] {
	flex: 1;
	padding: 0.625rem 0.78125rem;
	border: 0.0625rem solid #e9ecef;
	border-radius: 0.3125rem;
	font-size: 0.875rem;
	background-color: #f8f9fa;
	transition: all 0.3s ease;
}
.form-input[data-v-6e77590c]:focus {
	border-color: #2e7d32;
	background-color: #fff;
	outline: none;
	box-shadow: 0 0 0 0.125rem rgba(76, 175, 80, 0.1);
}

/* 当建议列表显示时，修改输入框下边框样式 */
.name-input-wrapper.suggestions-active .form-input[data-v-6e77590c] {
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
	border-bottom-color: transparent;
}
.input-icon[data-v-6e77590c]:hover {
	background-color: #e0e0e0;
}

/* 姓名建议列表 */
.name-suggestions[data-v-6e77590c] {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background-color: #fff;
	border: 0.0625rem solid #e0e0e0;
	border-top: none;
	border-radius: 0 0 0.25rem 0.25rem;
	max-height: 12.5rem;
	overflow-y: auto;
	z-index: 1000;
	box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.1);
	margin-top: 0.0625rem;
}
.suggestion-item[data-v-6e77590c] {
	padding: 0.625rem 0.9375rem;
	border-bottom: 0.03125rem solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.3s ease;
	font-size: 1rem;
	color: #333;
}
.suggestion-item[data-v-6e77590c]:last-child {
	border-bottom: none;
}
.suggestion-item[data-v-6e77590c]:hover {
	background-color: #f8f9fa;
}

/* 姓名建议遮罩层 */
.suggestions-overlay[data-v-6e77590c] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	background-color: transparent;
}

/* 日期范围选择样式 */
.date-range-inputs[data-v-6e77590c] {
	display: flex;
	align-items: center;
	gap: 0.46875rem;
	flex-wrap: wrap;
}

/* 在小屏幕上调整布局 */
@media (max-width: 600rpx) {
.date-range-inputs[data-v-6e77590c] {
		flex-direction: column;
		gap: 0.625rem;
		align-items: stretch;
}
.date-separator[data-v-6e77590c] {
		text-align: center;
		padding: 0.3125rem 0;
}
.reset-button[data-v-6e77590c] {
		align-self: center;
		min-width: 0.625rem;
}
}
.date-picker[data-v-6e77590c] {
	flex: 1;
	padding: 0.625rem 0.78125rem;
	background-color: #f8f9fa;
	border: 0.0625rem solid #e9ecef;
	border-radius: 0.3125rem;
	text-align: center;
	transition: all 0.3s ease;
}
.date-picker[data-v-6e77590c]:hover {
	background-color: #e9ecef;
	border-color: #2e7d32;
}
.date-text[data-v-6e77590c] {
	font-size: 0.875rem;
	color: #2e7d32;
	font-weight: 500;
}
.date-separator[data-v-6e77590c] {
	font-size: 0.875rem;
	color: #666;
	font-weight: 500;
	padding: 0 0.3125rem;
}

/* 回到本月按钮 */
.reset-button[data-v-6e77590c] {
	padding: 0.625rem 0.78125rem;
	background-color: #2e7d32;
	border: 0.0625rem solid #2e7d32;
	border-radius: 0.3125rem;
	cursor: pointer;
	transition: all 0.3s ease;
	white-space: nowrap;
	flex-shrink: 0;
}
.reset-button[data-v-6e77590c]:hover {
	background-color: #45a049;
	border-color: #45a049;
	transform: translateY(-0.03125rem);
	box-shadow: 0 0.125rem 0.375rem rgba(76, 175, 80, 0.3);
}
.reset-button[data-v-6e77590c]:active {
	transform: translateY(0);
	box-shadow: 0 0.0625rem 0.1875rem rgba(76, 175, 80, 0.3);
}
.reset-text[data-v-6e77590c] {
	font-size: 0.75rem;
	color: white;
	font-weight: 500;
}

/* 工人概览 */
.worker-overview[data-v-6e77590c] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.overview-title[data-v-6e77590c] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.overview-cards[data-v-6e77590c] {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.625rem;
}
.overview-card[data-v-6e77590c] {
	text-align: center;
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
}
.card-value[data-v-6e77590c] {
	display: block;
	font-size: 1.125rem;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 0.25rem;
}
.card-label[data-v-6e77590c] {
	font-size: 0.8125rem;
	color: #666;
}

/* 分析部分 */
.attendance-section[data-v-6e77590c],
.earnings-section[data-v-6e77590c],
.ranking-section[data-v-6e77590c] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.section-title[data-v-6e77590c] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}

/* 工作模式分布 */
.work-mode-distribution[data-v-6e77590c] {
	margin-bottom: 0.9375rem;
}
.distribution-title[data-v-6e77590c] {
	font-size: 0.875rem;
	color: #666;
	margin-bottom: 0.46875rem;
}
.mode-stats[data-v-6e77590c] {
	display: flex;
	gap: 0.625rem;
}
.mode-item[data-v-6e77590c] {
	flex: 1;
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
	text-align: center;
}
.mode-name[data-v-6e77590c] {
	display: block;
	font-size: 0.8125rem;
	color: #666;
	margin-bottom: 0.25rem;
}
.mode-days[data-v-6e77590c] {
	display: block;
	font-size: 1rem;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 0.125rem;
}
.mode-percentage[data-v-6e77590c] {
	font-size: 0.75rem;
	color: #999;
}



/* 统计网格 */
.earnings-stats[data-v-6e77590c] {
	margin-bottom: 0.9375rem;
}
.stats-title[data-v-6e77590c] {
	font-size: 0.875rem;
	color: #666;
	margin-bottom: 0.46875rem;
}
.stats-grid[data-v-6e77590c] {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.46875rem;
}
.stat-item[data-v-6e77590c],
.efficiency-item[data-v-6e77590c] {
	padding: 0.46875rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
	text-align: center;
}
.stat-label[data-v-6e77590c],
.efficiency-label[data-v-6e77590c] {
	display: block;
	font-size: 0.75rem;
	color: #666;
	margin-bottom: 0.25rem;
}
.stat-value[data-v-6e77590c],
.efficiency-value[data-v-6e77590c] {
	display: block;
	font-size: 0.875rem;
	font-weight: 600;
	color: #2e7d32;
}

/* 排名卡片 */
.ranking-cards[data-v-6e77590c] {
	display: flex;
	flex-direction: column;
	gap: 0.46875rem;
}
.ranking-card[data-v-6e77590c] {
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
	text-align: center;
}
.ranking-title[data-v-6e77590c] {
	display: block;
	font-size: 0.875rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.25rem;
}
.ranking-position[data-v-6e77590c] {
	display: block;
	font-size: 1.125rem;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 0.25rem;
}
.ranking-desc[data-v-6e77590c] {
	font-size: 0.75rem;
	color: #666;
	margin-bottom: 0.15625rem;
}
.ranking-percent[data-v-6e77590c] {
	font-size: 0.6875rem;
	color: #2e7d32;
	font-weight: 500;
}

/* 无数据提示 */
.no-data[data-v-6e77590c] {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 3.125rem 1.25rem;
	text-align: center;
}
.no-data-icon[data-v-6e77590c] {
	font-size: 3.75rem;
	margin-bottom: 0.9375rem;
}
.no-data-title[data-v-6e77590c] {
	font-size: 1.125rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.no-data-desc[data-v-6e77590c] {
	font-size: 0.875rem;
	color: #666;
	line-height: 1.5;
}

/* 姓名库弹窗样式 */
.modal-overlay[data-v-6e77590c] {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}
.modal-content[data-v-6e77590c] {
	background-color: white;
	border-radius: 0.375rem;
	width: 80%;
	max-width: 18.75rem;
	max-height: 80vh;
	overflow: hidden;
}
.modal-header[data-v-6e77590c] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.9375rem;
	border-bottom: 0.03125rem solid #f0f0f0;
}
.modal-title[data-v-6e77590c] {
	font-size: 1.125rem;
	font-weight: 600;
	color: #333;
}
.modal-close[data-v-6e77590c] {
	font-size: 1.25rem;
	color: #999;
	cursor: pointer;
	padding: 0.3125rem;
}
.name-list[data-v-6e77590c] {
	max-height: 60vh;
	overflow-y: auto;
}
.name-item[data-v-6e77590c] {
	padding: 0.78125rem 0.9375rem;
	border-bottom: 0.03125rem solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.3s ease;
}
.name-item[data-v-6e77590c]:last-child {
	border-bottom: none;
}
.name-item[data-v-6e77590c]:hover {
	background-color: #f8f9fa;
}
.name-content[data-v-6e77590c] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.name-text[data-v-6e77590c] {
	font-size: 1rem;
	color: #333;
}


.financial-analysis[data-v-7d43d468] {
	padding: 0.625rem;
}

/* 月份选择器 */
.month-selector[data-v-7d43d468] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.selector-header[data-v-7d43d468] {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.selector-title[data-v-7d43d468] {
	font-size: 1.125rem;
	font-weight: 600;
	color: #333;
}
.month-picker[data-v-7d43d468] {
	display: flex;
	align-items: center;
	padding: 0.3125rem 0.625rem;
	background-color: #f5f5f5;
	border-radius: 0.25rem;
}
.month-text[data-v-7d43d468] {
	font-size: 1rem;
	color: #2e7d32;
	margin-right: 0.3125rem;
}
.picker-arrow[data-v-7d43d468] {
	font-size: 0.75rem;
}

/* 财务概览 */
.financial-overview[data-v-7d43d468] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.overview-title[data-v-7d43d468] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.overview-cards[data-v-7d43d468] {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.625rem;
}
.overview-card[data-v-7d43d468] {
	text-align: center;
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
	transition: all 0.3s ease;
	border: 0.0625rem solid transparent;
}
.overview-card.tea-picking-expense[data-v-7d43d468] {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: white;
}
.overview-card.labor-expense[data-v-7d43d468] {
	background: linear-gradient(135deg, #fa8c16, #ffa940);
	color: white;
}
.overview-card.highlight[data-v-7d43d468] {
	background: linear-gradient(135deg, #2e7d32, #45a049);
	color: white;
}
.overview-card.total-expense[data-v-7d43d468] {
	background: linear-gradient(135deg, #f44336, #e57373);
}
.overview-card.total-income[data-v-7d43d468] {
	background: linear-gradient(135deg, #1976d2, #42a5f5);
}
.overview-card.gross-profit.profit-positive[data-v-7d43d468] {
	background: linear-gradient(135deg, #2e7d32, #66bb6a);
}
.overview-card.gross-profit.profit-negative[data-v-7d43d468] {
	background: linear-gradient(135deg, #d32f2f, #f44336);
}
.overview-card.highlight .card-value[data-v-7d43d468],
.overview-card.highlight .card-label[data-v-7d43d468],
.overview-card.tea-picking-expense .card-value[data-v-7d43d468],
.overview-card.tea-picking-expense .card-label[data-v-7d43d468],
.overview-card.labor-expense .card-value[data-v-7d43d468],
.overview-card.labor-expense .card-label[data-v-7d43d468] {
	color: white;
}
.card-value[data-v-7d43d468] {
	display: block;
	font-size: 1.125rem;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 0.25rem;
}
.card-label[data-v-7d43d468] {
	font-size: 0.875rem;
	color: #666;
}

/* 分析部分 */
.expense-section[data-v-7d43d468],
.income-section[data-v-7d43d468],
.cost-benefit-section[data-v-7d43d468],
.comparison-section[data-v-7d43d468],
.budget-suggestions[data-v-7d43d468] {
	background-color: white;
	border-radius: 0.375rem;
	padding: 0.9375rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.1);
}
.section-title[data-v-7d43d468] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}

/* 统计容器 - 参考MonthlyAnalysis.vue */
.daily-expense-analysis[data-v-7d43d468],
.daily-income-analysis[data-v-7d43d468],
.tea-cost-analysis[data-v-7d43d468],
.stats-container[data-v-7d43d468] {
	margin-bottom: 0.9375rem;
}
.analysis-title[data-v-7d43d468],
.stats-title[data-v-7d43d468] {
	font-size: 0.875rem;
	color: #666;
	margin-bottom: 0.46875rem;
}

/* 统计网格 - 参考MonthlyAnalysis.vue */
.expense-stats[data-v-7d43d468],
.income-stats[data-v-7d43d468],
.cost-efficiency-grid[data-v-7d43d468],
.stats-grid[data-v-7d43d468] {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 0.46875rem;
}

/* 两列布局的统计网格 */
.expense-stats[data-v-7d43d468],
.income-stats[data-v-7d43d468],
.cost-efficiency-grid[data-v-7d43d468] {
	grid-template-columns: repeat(2, 1fr);
}
.stat-item[data-v-7d43d468],
.efficiency-item[data-v-7d43d468] {
	padding: 0.46875rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
	text-align: center;
}
.stat-label[data-v-7d43d468],
.efficiency-label[data-v-7d43d468] {
	display: block;
	font-size: 0.875rem;
	color: #666;
	margin-bottom: 0.15625rem;
}
.stat-value[data-v-7d43d468],
.efficiency-value[data-v-7d43d468] {
	display: block;
	font-size: 1.125rem;
	font-weight: 600;
	color: #2e7d32;
}

/* 对比分析 */
.comparison-cards[data-v-7d43d468] {
	display: flex;
	flex-direction: column;
	gap: 0.46875rem;
}
.comparison-card[data-v-7d43d468] {
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.25rem;
}
.comparison-title[data-v-7d43d468] {
	font-size: 0.875rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.comparison-item[data-v-7d43d468] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.375rem;
	padding-top: 0.0625rem;
}
.comparison-item[data-v-7d43d468]:first-of-type {
	margin-top: 0.25rem;
}
.comparison-label[data-v-7d43d468] {
	font-size: 0.875rem;
	color: #666;
}
.comparison-value[data-v-7d43d468] {
	font-size: 0.875rem;
	font-weight: 600;
}
.comparison-value.positive[data-v-7d43d468] {
	color: #2e7d32;
}
.comparison-value.negative[data-v-7d43d468] {
	color: #f44336;
}


.comprehensive-stats-page[data-v-1608839e] {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 权限错误页面 */
.permission-error[data-v-1608839e] {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 100vh;
	padding: 1.25rem;
	text-align: center;
}
.error-icon[data-v-1608839e] {
	font-size: 3.75rem;
	margin-bottom: 0.9375rem;
}
.error-title[data-v-1608839e] {
	font-size: 1.5rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.error-desc[data-v-1608839e] {
	font-size: 1rem;
	color: #666;
	margin-bottom: 1.875rem;
	line-height: 1.5;
}
.back-btn[data-v-1608839e] {
	padding: 0.625rem 1.25rem;
	background-color: #2e7d32;
	color: white;
	border: none;
	border-radius: 0.375rem;
	font-size: 1rem;
}

/* 统计内容 */
.stats-content[data-v-1608839e] {
	padding-bottom: 3.75rem;
}

/* 标签导航 */
.tab-navigation[data-v-1608839e] {
	display: flex;
	background-color: white;
	border-bottom: 0.0625rem solid #f0f0f0;
	position: -webkit-sticky;
	position: sticky;
	top: 0;
	z-index: 100;
}
.tab-item[data-v-1608839e] {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 0.625rem 0.3125rem;
	cursor: pointer;
	transition: all 0.3s ease;
}
.tab-item.active[data-v-1608839e] {
	background-color: #2e7d32;
	color: white;
}
.tab-item.active .tab-icon[data-v-1608839e],
.tab-item.active .tab-text[data-v-1608839e] {
	color: white;
}
.tab-icon[data-v-1608839e] {
	font-size: 1.125rem;
	margin-bottom: 0.25rem;
}
.tab-text[data-v-1608839e] {
	font-size: 0.75rem;
	color: #666;
}

/* 加载状态 */
.loading-container[data-v-1608839e] {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 3.125rem 0;
}
.loading-text[data-v-1608839e] {
	font-size: 1rem;
	color: #999;
}

/* 分析容器 */
.analysis-container[data-v-1608839e] {
	padding: 0.625rem;
}


