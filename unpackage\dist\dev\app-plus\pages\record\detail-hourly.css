
.detail-page[data-v-7013ad66] {
	min-height: 100vh;
	background-color: #f5f5f5;
	/* 适配状态栏高度 - 多种兼容方案 */
	padding-top: constant(safe-area-inset-top);
	/* iOS 11.0-11.2 */
	padding-top: env(safe-area-inset-top);
	/* iOS 11.2+ */
	/* 备用方案 */
	padding-top: var(--status-bar-height, 0px);
}

/* 导航栏样式 - 时工模式蓝色主题 */
.nav-bar[data-v-7013ad66] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.625rem 0.9375rem;
	background: linear-gradient(135deg, #2196F3 0%, #1565C0 100%);
	border-bottom: 0.0625rem solid rgba(33, 150, 243, 0.3);
	position: -webkit-sticky;
	position: sticky;
	top: 0;
	z-index: 100;
	box-shadow: 0 0.0625rem 0.25rem rgba(33, 150, 243, 0.2);
	/* 确保导航栏不会被状态栏遮挡 - 多种兼容方案 */
	margin-top: calc(-1 * constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	margin-top: calc(-1 * env(safe-area-inset-top));
	/* iOS 11.2+ */
	margin-top: calc(-1 * var(--status-bar-height, 0px));
	/* 备用方案 */

	padding-top: calc(0.625rem + constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	padding-top: calc(0.625rem + env(safe-area-inset-top));
	/* iOS 11.2+ */
	padding-top: calc(0.625rem + var(--status-bar-height, 0px));
	/* 备用方案 */
}
.nav-left[data-v-7013ad66] {
	display: flex;
	align-items: center;
	gap: 0.3125rem;
	cursor: pointer;
}
.nav-icon[data-v-7013ad66] {
	font-size: 1.125rem;
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.nav-text[data-v-7013ad66] {
	font-size: 1rem;
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.nav-title[data-v-7013ad66] {
	font-size: 1.125rem;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.nav-right[data-v-7013ad66] {
	min-width: 3.125rem;
	text-align: right;
}
.edit-btn[data-v-7013ad66] {
	padding: 0.375rem 0.75rem;
	background-color: rgba(255, 255, 255, 0.2);
	color: white;
	border: 0.03125rem solid rgba(255, 255, 255, 0.3);
	border-radius: 0.625rem;
	font-size: 0.875rem;
	cursor: pointer;
	-webkit-backdrop-filter: blur(0.3125rem);
	        backdrop-filter: blur(0.3125rem);
}
.edit-btn[data-v-7013ad66]:active {
	background-color: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}

/* 编辑模式按钮组 */
.edit-actions[data-v-7013ad66] {
	display: flex;
	gap: 0.46875rem;
}
.save-btn[data-v-7013ad66],
.cancel-btn[data-v-7013ad66] {
	padding: 0.375rem 0.75rem;
	border-radius: 0.625rem;
	font-size: 0.875rem;
	cursor: pointer;
	transition: all 0.3s ease;
}
.save-btn[data-v-7013ad66] {
	background-color: #f24251;
	color: white;
}
.save-btn.disabled[data-v-7013ad66] {
	background-color: #ccc;
	cursor: not-allowed;
}
.save-btn[data-v-7013ad66]:not(.disabled):active {
	background-color: #e11919;
}
.cancel-btn[data-v-7013ad66] {
	background-color: #2545d4;
	color: white;
}
.cancel-btn[data-v-7013ad66]:active {
	background-color: #423d3d;
}

/* 编辑表单样式 */
.edit-input[data-v-7013ad66] {
	padding: 0.5rem 0.625rem;
	border: 0.0625rem solid #e0e0e0;
	border-radius: 0.25rem;
	font-size: 1rem;
	color: #333;
	background-color: #fff;
	transition: border-color 0.3s ease;
}
.edit-input[data-v-7013ad66]:focus {
	border-color: #2196F3;
	outline: none;
}
.number-input[data-v-7013ad66] {
	text-align: right;
}
.edit-picker[data-v-7013ad66] {
	padding: 0.5rem 0.625rem;
	border: 0.0625rem solid #e0e0e0;
	border-radius: 0.25rem;
	background-color: #fff;
	cursor: pointer;
}
.picker-text[data-v-7013ad66] {
	font-size: 1rem;
	color: #333;
}

/* 加载和错误状态 */
.loading-container[data-v-7013ad66],
.error-container[data-v-7013ad66] {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 3.125rem 1.25rem;
	text-align: center;
}
.loading-text[data-v-7013ad66] {
	font-size: 1rem;
	color: #666;
}
.error-icon[data-v-7013ad66] {
	font-size: 2.5rem;
	margin-bottom: 0.625rem;
}
.error-text[data-v-7013ad66] {
	font-size: 1rem;
	color: #666;
	margin-bottom: 1.25rem;
}
.back-btn[data-v-7013ad66] {
	padding: 0.625rem 1.25rem;
	background-color: #2196F3;
	color: white;
	border: none;
	border-radius: 0.78125rem;
	font-size: 1rem;
}

/* 详情内容 */
.detail-content[data-v-7013ad66] {
	padding: 0.625rem;
}

/* 卡片通用样式 */
.info-card[data-v-7013ad66],
.detail-card[data-v-7013ad66],
.summary-card[data-v-7013ad66] {
	background-color: #fff;
	border-radius: 0.46875rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.1);
	overflow: hidden;
}
.card-header[data-v-7013ad66] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.9375rem;
	background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
	color: white;
}
.card-title[data-v-7013ad66] {
	font-size: 1rem;
	font-weight: 600;
}
.work-mode-badge[data-v-7013ad66] {
	padding: 0.25rem 0.5rem;
	border-radius: 0.625rem;
	font-size: 0.75rem;
	background-color: rgba(255, 255, 255, 0.2);
}
.detail-mode[data-v-7013ad66] {
	font-size: 0.8125rem;
	opacity: 0.9;
}

/* 基本信息网格 */
.info-grid[data-v-7013ad66] {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0.9375rem;
	padding: 0.9375rem;
}
.info-item[data-v-7013ad66] {
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
}
.info-label[data-v-7013ad66] {
	font-size: 0.8125rem;
	color: #666;
	font-weight: 500;
}
.info-value[data-v-7013ad66] {
	font-size: 1rem;
	color: #333;
	font-weight: 600;
}
.info-value.earnings[data-v-7013ad66] {
	color: #2196F3;
	font-size: 1.125rem;
}

/* 时工内容 */
.hourly-content[data-v-7013ad66] {
	padding: 0.9375rem;
}
.mode-title[data-v-7013ad66] {
	font-size: 0.9375rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
	text-align: center;
}

/* 简化模式 */
.simple-grid[data-v-7013ad66] {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	gap: 0.625rem;
}
.simple-item[data-v-7013ad66] {
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
	text-align: center;
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.375rem;
}
.simple-label[data-v-7013ad66] {
	font-size: 0.8125rem;
	color: #666;
}
.simple-value[data-v-7013ad66] {
	font-size: 0.9375rem;
	color: #333;
	font-weight: 600;
}
.simple-value.earnings[data-v-7013ad66] {
	color: #2196F3;
}

/* 详细模式 */
.time-period[data-v-7013ad66] {
	border: 0.0625rem solid #f0f0f0;
	border-radius: 0.375rem;
	margin-bottom: 0.625rem;
	overflow: hidden;
}
.time-period[data-v-7013ad66]:last-child {
	margin-bottom: 0;
}
.period-header[data-v-7013ad66] {
	padding: 0.625rem 0.75rem;
	background-color: #f8f9fa;
	border-bottom: 0.0625rem solid #f0f0f0;
}
.period-title[data-v-7013ad66] {
	font-size: 0.875rem;
	font-weight: 600;
	color: #2196F3;
}
.period-grid[data-v-7013ad66] {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	gap: 0.625rem;
	padding: 0.75rem;
}
.period-item[data-v-7013ad66] {
	display: flex;
	flex-direction: column;
	gap: 0.1875rem;
	text-align: center;
}
.period-label[data-v-7013ad66] {
	font-size: 0.75rem;
	color: #666;
}
.period-value[data-v-7013ad66] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
}
.period-value.earnings[data-v-7013ad66] {
	color: #2196F3;
	font-weight: 600;
}

/* 统计汇总网格 */
.summary-grid[data-v-7013ad66] {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0.9375rem;
	padding: 0.9375rem;
}
.summary-item[data-v-7013ad66] {
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
	text-align: center;
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.375rem;
}
.summary-label[data-v-7013ad66] {
	font-size: 0.8125rem;
	color: #666;
}
.summary-value[data-v-7013ad66] {
	font-size: 1rem;
	color: #333;
	font-weight: 600;
}
.summary-value.earnings[data-v-7013ad66] {
	color: #2196F3;
	font-size: 1.125rem;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.info-grid[data-v-7013ad66],
	.summary-grid[data-v-7013ad66] {
		grid-template-columns: 1fr;
		gap: 0.625rem;
}
.simple-grid[data-v-7013ad66],
	.period-grid[data-v-7013ad66] {
		grid-template-columns: 1fr;
		gap: 0.46875rem;
}
.nav-title[data-v-7013ad66] {
		font-size: 1rem;
}
.edit-btn[data-v-7013ad66] {
		font-size: 0.8125rem;
		padding: 0.3125rem 0.625rem;
}
}

/* 月报模式青色主题 */
.monthly-theme .nav-bar[data-v-7013ad66] {
	background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);
	border-bottom: 0.0625rem solid rgba(0, 188, 212, 0.3);
	box-shadow: 0 0.0625rem 0.25rem rgba(0, 188, 212, 0.2);
}
.monthly-theme .nav-icon[data-v-7013ad66] {
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.monthly-theme .nav-text[data-v-7013ad66] {
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.monthly-theme .nav-title[data-v-7013ad66] {
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.monthly-theme .edit-btn[data-v-7013ad66] {
	background-color: rgba(255, 255, 255, 0.2);
	color: white;
	border: 0.03125rem solid rgba(255, 255, 255, 0.3);
}
.monthly-theme .edit-btn[data-v-7013ad66]:active {
	background-color: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}
.monthly-theme .save-btn[data-v-7013ad66] {
	background-color: #f24251;
}
.monthly-theme .save-btn[data-v-7013ad66]:not(.disabled):active {
	background-color: #e11919;
}
.monthly-theme .back-btn[data-v-7013ad66] {
	background-color: #00BCD4;
}
.monthly-theme .card-header[data-v-7013ad66] {
	background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);
}
.monthly-theme .info-value.earnings[data-v-7013ad66],
.monthly-theme .simple-value.earnings[data-v-7013ad66],
.monthly-theme .period-value.earnings[data-v-7013ad66],
.monthly-theme .summary-value.earnings[data-v-7013ad66] {
	color: #00BCD4;
}
.monthly-theme .period-title[data-v-7013ad66] {
	color: #00BCD4;
}
.monthly-theme .edit-input[data-v-7013ad66]:focus {
	border-color: #00BCD4;
}
