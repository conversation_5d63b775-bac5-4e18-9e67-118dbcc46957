/**
 * 调试辅助工具
 * 用于排查和修复工作记录删除功能问题
 */

class DebugHelper {
    constructor() {
        this.logPrefix = '🔧 [调试助手]'
    }

    /**
     * 检查用户状态
     */
    checkUserStatus() {
        console.log(`${this.logPrefix} 开始检查用户状态`)
        
        // 检查本地存储
        const userInfo = uni.getStorageSync('userInfo')
        const token = uni.getStorageSync('token')
        
        console.log(`${this.logPrefix} 本地存储用户信息:`, userInfo)
        console.log(`${this.logPrefix} 本地存储Token:`, token)
        
        // 检查Vuex状态（如果可用）
        if (typeof getApp === 'function') {
            try {
                const app = getApp()
                if (app.$store) {
                    const storeUserInfo = app.$store.getters['user/userInfo']
                    const storeIsAdmin = app.$store.getters['user/isAdmin']
                    const storeIsLoggedIn = app.$store.getters['user/isLoggedIn']
                    
                    console.log(`${this.logPrefix} Vuex用户信息:`, storeUserInfo)
                    console.log(`${this.logPrefix} Vuex管理员状态:`, storeIsAdmin)
                    console.log(`${this.logPrefix} Vuex登录状态:`, storeIsLoggedIn)
                }
            } catch (error) {
                console.warn(`${this.logPrefix} 无法访问Vuex状态:`, error)
            }
        }
        
        return {
            localUserInfo: userInfo,
            localToken: token,
            hasValidSession: !!(userInfo && token)
        }
    }

    /**
     * 重置用户状态为管理员
     */
    resetToAdmin() {
        console.log(`${this.logPrefix} 重置用户状态为管理员`)
        
        const adminUserInfo = {
            id: 1,
            username: 'admin',
            real_name: '管理员',
            user_type: 'admin',
            created_at: new Date().toISOString()
        }
        
        const adminToken = 'debug_admin_token_' + Date.now()
        
        // 保存到本地存储
        uni.setStorageSync('userInfo', adminUserInfo)
        uni.setStorageSync('token', adminToken)
        
        console.log(`${this.logPrefix} 管理员状态已设置:`, adminUserInfo)
        
        // 如果可以访问Vuex，也更新Vuex状态
        if (typeof getApp === 'function') {
            try {
                const app = getApp()
                if (app.$store) {
                    app.$store.commit('user/SET_USER_INFO', adminUserInfo)
                    app.$store.commit('user/SET_TOKEN', adminToken)
                    console.log(`${this.logPrefix} Vuex状态已更新`)
                }
            } catch (error) {
                console.warn(`${this.logPrefix} 无法更新Vuex状态:`, error)
            }
        }
        
        return adminUserInfo
    }

    /**
     * 清除所有用户数据
     */
    clearUserData() {
        console.log(`${this.logPrefix} 清除所有用户数据`)
        
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('token')
        
        // 如果可以访问Vuex，也清除Vuex状态
        if (typeof getApp === 'function') {
            try {
                const app = getApp()
                if (app.$store) {
                    app.$store.commit('user/CLEAR_USER_DATA')
                    console.log(`${this.logPrefix} Vuex状态已清除`)
                }
            } catch (error) {
                console.warn(`${this.logPrefix} 无法清除Vuex状态:`, error)
            }
        }
        
        console.log(`${this.logPrefix} 用户数据清除完成`)
    }

    /**
     * 检查删除功能依赖
     */
    checkDeleteDependencies() {
        console.log(`${this.logPrefix} 检查删除功能依赖`)
        
        const dependencies = {
            SalesManager: null,
            formatDate: null,
            formatCurrency: null,
            showModal: null
        }
        
        // 检查SalesManager
        try {
            const SalesManager = require('@/utils/salesManager.js').default || require('@/utils/salesManager.js')
            dependencies.SalesManager = !!SalesManager
            console.log(`${this.logPrefix} SalesManager可用:`, dependencies.SalesManager)
        } catch (error) {
            dependencies.SalesManager = false
            console.error(`${this.logPrefix} SalesManager不可用:`, error)
        }
        
        // 检查uni API
        dependencies.showModal = typeof uni !== 'undefined' && typeof uni.showModal === 'function'
        console.log(`${this.logPrefix} uni.showModal可用:`, dependencies.showModal)
        
        return dependencies
    }

    /**
     * 测试删除功能
     */
    testDeleteFunction(record) {
        console.log(`${this.logPrefix} 测试删除功能`)
        
        if (!record) {
            console.error(`${this.logPrefix} 测试失败：没有提供记录对象`)
            return false
        }
        
        console.log(`${this.logPrefix} 测试记录:`, record)
        
        // 检查用户状态
        const userStatus = this.checkUserStatus()
        if (!userStatus.hasValidSession) {
            console.warn(`${this.logPrefix} 测试警告：用户未登录`)
        }
        
        // 检查依赖
        const dependencies = this.checkDeleteDependencies()
        
        // 模拟删除确认对话框
        if (dependencies.showModal) {
            uni.showModal({
                title: '🔧 删除功能测试',
                content: `测试删除功能：

记录ID: ${record.id || '未知'}
工人: ${record.worker_name || '未知'}
日期: ${record.date || '未知'}
用户会话: ${userStatus.hasValidSession ? '有效' : '无效'}
SalesManager: ${dependencies.SalesManager ? '可用' : '不可用'}

这是一个测试，不会真正删除记录。`,
                showCancel: true,
                confirmText: '继续测试',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        console.log(`${this.logPrefix} 用户确认继续测试`)
                        this.simulateDeleteProcess(record)
                    } else {
                        console.log(`${this.logPrefix} 用户取消测试`)
                    }
                }
            })
        } else {
            console.error(`${this.logPrefix} 无法显示测试对话框：uni.showModal不可用`)
        }
        
        return true
    }

    /**
     * 模拟删除过程
     */
    simulateDeleteProcess(record) {
        console.log(`${this.logPrefix} 模拟删除过程开始`)
        
        // 模拟检查关联销售记录
        console.log(`${this.logPrefix} 模拟检查关联销售记录...`)
        
        // 模拟构建确认信息
        console.log(`${this.logPrefix} 模拟构建确认信息...`)
        
        // 模拟删除操作
        console.log(`${this.logPrefix} 模拟删除操作...`)
        
        // 模拟成功反馈
        setTimeout(() => {
            console.log(`${this.logPrefix} 模拟删除过程完成`)
            if (typeof uni !== 'undefined' && typeof uni.showToast === 'function') {
                uni.showToast({
                    title: '🔧 删除功能测试完成',
                    icon: 'success',
                    duration: 2000
                })
            }
        }, 1000)
    }

    /**
     * 获取调试报告
     */
    getDebugReport() {
        console.log(`${this.logPrefix} 生成调试报告`)
        
        const userStatus = this.checkUserStatus()
        const dependencies = this.checkDeleteDependencies()
        
        const report = {
            timestamp: new Date().toISOString(),
            userStatus,
            dependencies,
            systemInfo: uni.getSystemInfoSync(),
            storageInfo: uni.getStorageInfoSync()
        }
        
        console.log(`${this.logPrefix} 调试报告:`, report)
        
        return report
    }
}

// 创建全局实例
const debugHelper = new DebugHelper()

// 在控制台中提供快捷方法
if (typeof window !== 'undefined') {
    window.debugHelper = debugHelper
    window.checkUser = () => debugHelper.checkUserStatus()
    window.resetAdmin = () => debugHelper.resetToAdmin()
    window.clearUser = () => debugHelper.clearUserData()
    window.debugReport = () => debugHelper.getDebugReport()
    
    console.log('🔧 调试助手已加载，可用命令：')
    console.log('- checkUser() - 检查用户状态')
    console.log('- resetAdmin() - 重置为管理员')
    console.log('- clearUser() - 清除用户数据')
    console.log('- debugReport() - 生成调试报告')
}

export default debugHelper
